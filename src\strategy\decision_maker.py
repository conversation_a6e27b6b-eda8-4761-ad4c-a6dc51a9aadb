"""
Strategy Decision Maker - متخذ القرارات
يجمع إشارات الطبقات الأربع ويتخذ القرار النهائي للتداول
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
import json
import os
import random

from .technical_analysis import TechnicalAnalysisLayer
from .quantitative_analysis import QuantitativeAnalysisLayer
from .behavioral_analysis import BehavioralAnalysisLayer
from .ai_analysis import AIAnalysisLayer

logger = logging.getLogger(__name__)

class StrategyDecisionMaker:
    """متخذ القرارات الاستراتيجي - يجمع الطبقات الأربع"""
    
    def __init__(self, data_dir: str = "data"):
        """تهيئة متخذ القرارات"""
        self.name = "Strategy Decision Maker"
        self.data_dir = data_dir
        
        # تهيئة الطبقات الأربع
        self.technical_layer = TechnicalAnalysisLayer()
        self.quantitative_layer = QuantitativeAnalysisLayer()
        self.behavioral_layer = BehavioralAnalysisLayer()
        self.ai_layer = AIAnalysisLayer()
        
        # إعدادات القرار - معايير متدرجة لتحقيق 80% نجاح
        self.min_confidence_threshold = 60  # الحد الأدنى للثقة (نبدأ بـ 60% ونرفعها تدريجياً)
        self.min_agreement_layers = 3  # الحد الأدنى لعدد الطبقات المتفقة (3 من 4)
        self.risk_management_enabled = True
        self.target_win_rate = 80  # نسبة النجاح المستهدفة

        # معايير متقدمة للإشارات القوية
        self.strong_signal_threshold = 75  # للإشارات القوية جداً
        self.medium_signal_threshold = 65  # للإشارات المتوسطة
        
        # أوقات التداول المسموحة (24/7 للفوركس)
        self.trading_hours = {
            'start': 0,  # 24/7 - الفوركس يتداول على مدار الساعة
            'end': 23    # 24/7 - ما عدا عطلة نهاية الأسبوع
        }
        
        # أزمنة انتهاء الصفقات المتاحة (بالدقائق)
        self.available_expiry_times = [1, 2, 3, 5]
        
        # تخزين الإشارات التاريخية
        self.signal_history = []
        
        logger.info(f"✅ {self.name} initialized with 4 analysis layers")
    
    def analyze_market(self, pair_name: str, candles: List[Dict[str, Any]],
                      indicators_data: Optional[Dict[str, Any]] = None,
                      pair_data: Optional[Dict[str, List[Dict[str, Any]]]] = None,
                      live_candle: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        تحليل السوق الشامل باستخدام الطبقات الأربع

        Args:
            pair_name: اسم زوج العملات
            candles: قائمة الشموع (آخر 100 شمعة)
            indicators_data: بيانات المؤشرات الفنية
            pair_data: بيانات أزواج أخرى للتحليل الكمي
            live_candle: الشمعة الحية الحالية (غير مكتملة)

        Returns:
            تحليل شامل من جميع الطبقات
        """
        try:
            logger.info(f"🔍 Starting comprehensive market analysis for {pair_name}")
            
            # التأكد من وجود بيانات كافية
            if len(candles) < 50:
                return {
                    'error': 'Insufficient data for analysis',
                    'required_candles': 50,
                    'available_candles': len(candles)
                }
            
            # أخذ آخر 100 شمعة للتحليل
            analysis_candles = candles[-100:] if len(candles) >= 100 else candles

            # إضافة الشمعة الحية إذا كانت متاحة
            if live_candle:
                analysis_candles = analysis_candles.copy()
                analysis_candles.append(live_candle)
                logger.info(f"📊 Including live candle in analysis for {pair_name}")
            
            # تحليل الطبقة الأولى: التحليل الفني
            logger.info("📊 Running Technical Analysis Layer...")
            technical_signal = self.technical_layer.generate_technical_signal(analysis_candles)
            
            # تحليل الطبقة الثانية: التحليل الكمي
            logger.info("🔢 Running Quantitative Analysis Layer...")
            quantitative_signal = self.quantitative_layer.generate_quantitative_signal(
                analysis_candles, pair_data, self.signal_history
            )
            
            # تحليل الطبقة الثالثة: التحليل السلوكي
            logger.info("🧠 Running Behavioral Analysis Layer...")
            behavioral_signal = self.behavioral_layer.generate_behavioral_signal(analysis_candles)
            
            # تحليل الطبقة الرابعة: الذكاء الاصطناعي
            logger.info("🤖 Running AI Analysis Layer...")
            ai_signal = self.ai_layer.generate_ai_signal(analysis_candles, indicators_data)
            
            # جمع جميع الإشارات
            all_signals = {
                'technical': technical_signal,
                'quantitative': quantitative_signal,
                'behavioral': behavioral_signal,
                'ai': ai_signal
            }
            
            logger.info("✅ All analysis layers completed")
            
            return {
                'pair_name': pair_name,
                'analysis_timestamp': datetime.now().isoformat(),
                'candles_analyzed': len(analysis_candles),
                'layer_signals': all_signals,
                'analysis_summary': self._create_analysis_summary(all_signals)
            }
            
        except Exception as e:
            logger.error(f"Error in market analysis: {e}")
            return {
                'error': str(e),
                'pair_name': pair_name,
                'timestamp': datetime.now().isoformat()
            }
    
    def make_trading_decision(self, market_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        اتخاذ قرار التداول النهائي
        
        Args:
            market_analysis: نتائج تحليل السوق
            
        Returns:
            قرار التداول النهائي مع زمن انتهاء الصفقة
        """
        try:
            if 'error' in market_analysis:
                return {
                    'decision': 'NO_TRADE',
                    'reason': 'Analysis error',
                    'error': market_analysis['error']
                }
            
            layer_signals = market_analysis.get('layer_signals', {})
            
            # حساب النتيجة المجمعة
            combined_score = self._calculate_combined_score(layer_signals)
            
            # تحليل اتفاق الطبقات
            layer_agreement = self._analyze_layer_agreement(layer_signals)
            
            # فحص شروط التداول
            trading_conditions = self._check_trading_conditions()
            
            # اتخاذ القرار النهائي
            final_decision = self._make_final_decision(
                combined_score, layer_agreement, trading_conditions
            )
            
            # تحديد زمن انتهاء الصفقة
            expiry_time = self._determine_expiry_time(layer_signals, final_decision)
            
            # إنشاء الإشارة النهائية
            trading_signal = {
                'pair_name': market_analysis.get('pair_name', 'UNKNOWN'),
                'decision': final_decision['action'],
                'direction': final_decision['direction'],
                'confidence': final_decision['confidence'],
                'strength': final_decision['strength'],
                'expiry_minutes': expiry_time,
                'reasoning': final_decision['reasoning'],
                'layer_agreement': layer_agreement,
                'combined_score': combined_score,
                'trading_conditions': trading_conditions,
                'timestamp': datetime.now().isoformat(),
                'analysis_details': market_analysis
            }
            
            # حفظ الإشارة في التاريخ
            self._save_signal_to_history(trading_signal)
            
            logger.info(f"🎯 Trading decision: {final_decision['action']} - {final_decision['direction']} "
                       f"(Confidence: {final_decision['confidence']:.1f}%, Expiry: {expiry_time}min)")
            
            return trading_signal
            
        except Exception as e:
            logger.error(f"Error making trading decision: {e}")
            return {
                'decision': 'NO_TRADE',
                'reason': 'Decision error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _create_analysis_summary(self, all_signals: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء ملخص التحليل"""
        summary = {
            'layers_analyzed': 0,
            'layers_with_errors': 0,
            'bullish_signals': 0,
            'bearish_signals': 0,
            'neutral_signals': 0,
            'average_confidence': 0,
            'signal_distribution': {}
        }
        
        confidences = []
        
        for layer_name, signal in all_signals.items():
            if 'error' not in signal:
                summary['layers_analyzed'] += 1
                
                signal_direction = signal.get('signal', 'NEUTRAL')
                confidence = signal.get('confidence', 0)
                
                if signal_direction == 'CALL':
                    summary['bullish_signals'] += 1
                elif signal_direction == 'PUT':
                    summary['bearish_signals'] += 1
                else:
                    summary['neutral_signals'] += 1
                
                confidences.append(confidence)
                summary['signal_distribution'][layer_name] = {
                    'signal': signal_direction,
                    'confidence': confidence
                }
            else:
                summary['layers_with_errors'] += 1
        
        if confidences:
            summary['average_confidence'] = np.mean(confidences)
        
        return summary
    
    def _calculate_combined_score(self, layer_signals: Dict[str, Any]) -> Dict[str, Any]:
        """حساب النتيجة المجمعة من جميع الطبقات"""
        total_weighted_score = 0
        total_weight = 0
        layer_contributions = {}
        
        for layer_name, signal in layer_signals.items():
            if 'error' not in signal:
                weight = signal.get('weight', 0.25)
                strength = signal.get('strength', 0)
                signal_direction = signal.get('signal', 'NEUTRAL')
                
                # تحويل الإشارة إلى نقاط
                if signal_direction == 'CALL':
                    layer_score = strength
                elif signal_direction == 'PUT':
                    layer_score = -strength
                else:
                    layer_score = 0
                
                weighted_score = layer_score * weight
                total_weighted_score += weighted_score
                total_weight += weight
                
                layer_contributions[layer_name] = {
                    'score': layer_score,
                    'weight': weight,
                    'contribution': weighted_score
                }
        
        # تطبيع النتيجة النهائية
        final_score = total_weighted_score / total_weight if total_weight > 0 else 0
        
        return {
            'final_score': final_score,
            'total_weight': total_weight,
            'layer_contributions': layer_contributions,
            'direction': 'CALL' if final_score > 0 else 'PUT',
            'strength': abs(final_score)
        }
    
    def _analyze_layer_agreement(self, layer_signals: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل اتفاق الطبقات"""
        valid_signals = {}
        
        # جمع الإشارات الصحيحة
        for layer_name, signal in layer_signals.items():
            if 'error' not in signal:
                valid_signals[layer_name] = signal.get('signal', 'NEUTRAL')
        
        if not valid_signals:
            return {
                'agreement_level': 'NO_VALID_SIGNALS',
                'agreeing_layers': 0,
                'total_layers': 0,
                'consensus': 'NONE'
            }
        
        # حساب الاتفاق
        signal_counts = {'CALL': 0, 'PUT': 0, 'NEUTRAL': 0}
        for signal in valid_signals.values():
            signal_counts[signal] += 1
        
        total_layers = len(valid_signals)
        max_agreement = max(signal_counts.values())
        consensus_signal = max(signal_counts, key=signal_counts.get)
        
        # تحديد مستوى الاتفاق
        agreement_percentage = (max_agreement / total_layers) * 100
        
        if agreement_percentage >= 75:
            agreement_level = 'STRONG'
        elif agreement_percentage >= 50:
            agreement_level = 'MODERATE'
        else:
            agreement_level = 'WEAK'
        
        return {
            'agreement_level': agreement_level,
            'agreement_percentage': agreement_percentage,
            'agreeing_layers': max_agreement,
            'total_layers': total_layers,
            'consensus': consensus_signal,
            'signal_distribution': signal_counts,
            'layer_signals': valid_signals
        }

    def _check_trading_conditions(self) -> Dict[str, Any]:
        """فحص شروط التداول"""
        conditions = {
            'time_check': self._check_trading_hours(),
            'risk_check': self._check_risk_conditions(),
            'market_check': self._check_market_conditions(),
            'overall_status': True
        }

        # تحديد الحالة العامة
        conditions['overall_status'] = all([
            conditions['time_check']['allowed'],
            conditions['risk_check']['allowed'],
            conditions['market_check']['allowed']
        ])

        return conditions

    def _check_trading_hours(self) -> Dict[str, Any]:
        """فحص أوقات التداول"""
        current_hour = datetime.now().hour

        is_allowed = self.trading_hours['start'] <= current_hour <= self.trading_hours['end']

        return {
            'allowed': is_allowed,
            'current_hour': current_hour,
            'trading_window': f"{self.trading_hours['start']}:00 - {self.trading_hours['end']}:00",
            'reason': 'Within trading hours' if is_allowed else 'Outside trading hours'
        }

    def _check_risk_conditions(self) -> Dict[str, Any]:
        """فحص شروط إدارة المخاطر"""
        if not self.risk_management_enabled:
            return {'allowed': True, 'reason': 'Risk management disabled'}

        # فحص عدد الصفقات الأخيرة
        recent_signals = [s for s in self.signal_history
                         if self._is_recent_signal(s, hours=1)]

        max_trades_per_hour = 5
        trades_this_hour = len([s for s in recent_signals
                               if s.get('decision') == 'TRADE'])

        risk_allowed = trades_this_hour < max_trades_per_hour

        return {
            'allowed': risk_allowed,
            'trades_this_hour': trades_this_hour,
            'max_trades_per_hour': max_trades_per_hour,
            'reason': 'Within risk limits' if risk_allowed else 'Too many trades this hour'
        }

    def _check_market_conditions(self) -> Dict[str, Any]:
        """فحص ظروف السوق"""
        # فحص بسيط - يمكن تطويره أكثر
        return {
            'allowed': True,
            'volatility': 'NORMAL',
            'liquidity': 'GOOD',
            'reason': 'Market conditions acceptable'
        }

    def _is_recent_signal(self, signal: Dict[str, Any], hours: int = 1) -> bool:
        """فحص إذا كانت الإشارة حديثة"""
        try:
            signal_time = datetime.fromisoformat(signal.get('timestamp', ''))
            time_diff = datetime.now() - signal_time
            return time_diff <= timedelta(hours=hours)
        except:
            return False

    def _make_final_decision(self, combined_score: Dict[str, Any],
                           layer_agreement: Dict[str, Any],
                           trading_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """اتخاذ القرار النهائي"""

        # فحص الشروط الأساسية
        if not trading_conditions['overall_status']:
            return {
                'action': 'NO_TRADE',
                'direction': 'NEUTRAL',
                'confidence': 0,
                'strength': 0,
                'reasoning': f"Trading conditions not met: {self._get_failed_conditions(trading_conditions)}"
            }

        # فحص اتفاق الطبقات
        if layer_agreement['agreeing_layers'] < self.min_agreement_layers:
            return {
                'action': 'NO_TRADE',
                'direction': 'NEUTRAL',
                'confidence': 0,
                'strength': 0,
                'reasoning': f"Insufficient layer agreement: {layer_agreement['agreeing_layers']}/{layer_agreement['total_layers']}"
            }

        # حساب الثقة النهائية بطريقة محسنة
        strength = combined_score['strength']
        agreement_boost = layer_agreement['agreement_percentage'] / 100

        # حساب الثقة بناءً على أقوى إشارة + اتفاق الطبقات
        max_individual_confidence = max([
            signal.get('confidence', 0) for signal in layer_signals.values()
            if 'error' not in signal
        ] + [0])

        # دمج الثقة الفردية القصوى مع اتفاق الطبقات
        final_confidence = min(
            (max_individual_confidence * 0.7) + (agreement_boost * 30),
            100
        )

        # فحص الحد الأدنى للثقة
        if final_confidence < self.min_confidence_threshold:
            return {
                'action': 'NO_TRADE',
                'direction': 'NEUTRAL',
                'confidence': final_confidence,
                'strength': strength,
                'reasoning': f"Confidence too low: {final_confidence:.1f}% < {self.min_confidence_threshold}%"
            }

        # قرار التداول مع تصنيف القوة
        direction = combined_score['direction']

        # تحديد قوة الإشارة
        if final_confidence >= self.strong_signal_threshold:
            signal_strength = "VERY_STRONG"
        elif final_confidence >= self.medium_signal_threshold:
            signal_strength = "STRONG"
        else:
            signal_strength = "MODERATE"

        return {
            'action': 'TRADE',
            'direction': direction,
            'confidence': final_confidence,
            'strength': strength,
            'signal_strength': signal_strength,
            'max_individual_confidence': max_individual_confidence,
            'reasoning': f"{signal_strength} {direction} signal with {layer_agreement['agreeing_layers']}/{layer_agreement['total_layers']} layers agreeing (Max: {max_individual_confidence:.1f}%)"
        }

    def _get_failed_conditions(self, trading_conditions: Dict[str, Any]) -> str:
        """الحصول على الشروط الفاشلة"""
        failed = []

        for condition_name, condition_data in trading_conditions.items():
            if condition_name != 'overall_status' and isinstance(condition_data, dict):
                if not condition_data.get('allowed', True):
                    failed.append(condition_data.get('reason', condition_name))

        return ', '.join(failed) if failed else 'Unknown'

    def _determine_expiry_time(self, layer_signals: Dict[str, Any],
                             final_decision: Dict[str, Any]) -> int:
        """تحديد زمن انتهاء الصفقة"""

        if final_decision['action'] != 'TRADE':
            return 1  # افتراضي

        # تحليل قوة الإشارة لتحديد الزمن المناسب
        confidence = final_decision['confidence']
        strength = final_decision['strength']

        # حساب مؤشر التقلبات من الطبقات
        volatility_indicators = []

        for layer_name, signal in layer_signals.items():
            if 'error' not in signal:
                analysis_details = signal.get('analysis_details', {})

                # استخراج مؤشرات التقلبات من كل طبقة
                if layer_name == 'technical':
                    bb_analysis = analysis_details.get('bollinger_bands', {})
                    volatility_level = bb_analysis.get('volatility_level', 'NORMAL')
                    if volatility_level == 'HIGH':
                        volatility_indicators.append('HIGH')
                    elif volatility_level == 'LOW':
                        volatility_indicators.append('LOW')

                elif layer_name == 'quantitative':
                    volatility_analysis = analysis_details.get('volatility', {})
                    volatility_regime = volatility_analysis.get('volatility_regime', 'NORMAL_VOLATILITY')
                    if 'HIGH' in volatility_regime:
                        volatility_indicators.append('HIGH')
                    elif 'LOW' in volatility_regime:
                        volatility_indicators.append('LOW')

        # تحديد مستوى التقلبات العام
        high_vol_count = volatility_indicators.count('HIGH')
        low_vol_count = volatility_indicators.count('LOW')

        if high_vol_count > low_vol_count:
            volatility_level = 'HIGH'
        elif low_vol_count > high_vol_count:
            volatility_level = 'LOW'
        else:
            volatility_level = 'NORMAL'

        # تحديد زمن الانتهاء بناءً على القوة والتقلبات
        if confidence >= 80 and strength >= 0.7:
            # إشارة قوية جداً
            if volatility_level == 'HIGH':
                expiry = 1  # دقيقة واحدة للتقلبات العالية
            else:
                expiry = 2  # دقيقتان للتقلبات العادية/المنخفضة

        elif confidence >= 70 and strength >= 0.5:
            # إشارة قوية
            if volatility_level == 'HIGH':
                expiry = 2  # دقيقتان
            else:
                expiry = 3  # ثلاث دقائق

        else:
            # إشارة متوسطة
            if volatility_level == 'HIGH':
                expiry = 3  # ثلاث دقائق
            else:
                expiry = 5  # خمس دقائق

        # التأكد من أن الزمن ضمن الأزمنة المتاحة
        if expiry not in self.available_expiry_times:
            expiry = min(self.available_expiry_times, key=lambda x: abs(x - expiry))

        return expiry

    def _save_signal_to_history(self, trading_signal: Dict[str, Any]):
        """حفظ الإشارة في التاريخ"""
        try:
            # إضافة الإشارة للتاريخ
            self.signal_history.append(trading_signal)

            # الاحتفاظ بآخر 1000 إشارة فقط
            if len(self.signal_history) > 1000:
                self.signal_history = self.signal_history[-1000:]

            logger.debug(f"Signal saved to history. Total signals: {len(self.signal_history)}")

        except Exception as e:
            logger.error(f"Error saving signal to history: {e}")

    def get_strategy_performance(self) -> Dict[str, Any]:
        """الحصول على أداء الاستراتيجية"""
        try:
            if not self.signal_history:
                return {
                    'total_signals': 0,
                    'performance': 'No signals yet'
                }

            # إحصائيات عامة
            total_signals = len(self.signal_history)
            trade_signals = [s for s in self.signal_history if s.get('decision') == 'TRADE']
            no_trade_signals = [s for s in self.signal_history if s.get('decision') == 'NO_TRADE']

            # توزيع الاتجاهات
            call_signals = [s for s in trade_signals if s.get('direction') == 'CALL']
            put_signals = [s for s in trade_signals if s.get('direction') == 'PUT']

            # متوسط الثقة
            confidences = [s.get('confidence', 0) for s in trade_signals]
            avg_confidence = np.mean(confidences) if confidences else 0

            # توزيع أزمنة الانتهاء
            expiry_distribution = {}
            for signal in trade_signals:
                expiry = signal.get('expiry_minutes', 0)
                expiry_distribution[expiry] = expiry_distribution.get(expiry, 0) + 1

            # الإشارات الأخيرة
            recent_signals = self.signal_history[-10:] if len(self.signal_history) >= 10 else self.signal_history

            return {
                'total_signals': total_signals,
                'trade_signals': len(trade_signals),
                'no_trade_signals': len(no_trade_signals),
                'trade_ratio': len(trade_signals) / total_signals if total_signals > 0 else 0,
                'direction_distribution': {
                    'CALL': len(call_signals),
                    'PUT': len(put_signals)
                },
                'average_confidence': avg_confidence,
                'expiry_distribution': expiry_distribution,
                'recent_signals': recent_signals,
                'layer_performance': self._analyze_layer_performance()
            }

        except Exception as e:
            logger.error(f"Error calculating strategy performance: {e}")
            return {'error': str(e)}

    def _analyze_layer_performance(self) -> Dict[str, Any]:
        """تحليل أداء الطبقات"""
        layer_stats = {
            'technical': {'signals': 0, 'avg_confidence': 0, 'errors': 0},
            'quantitative': {'signals': 0, 'avg_confidence': 0, 'errors': 0},
            'behavioral': {'signals': 0, 'avg_confidence': 0, 'errors': 0},
            'ai': {'signals': 0, 'avg_confidence': 0, 'errors': 0}
        }

        for signal in self.signal_history:
            analysis_details = signal.get('analysis_details', {})
            layer_signals = analysis_details.get('layer_signals', {})

            for layer_name, layer_signal in layer_signals.items():
                if layer_name in layer_stats:
                    if 'error' in layer_signal:
                        layer_stats[layer_name]['errors'] += 1
                    else:
                        layer_stats[layer_name]['signals'] += 1
                        confidence = layer_signal.get('confidence', 0)
                        current_avg = layer_stats[layer_name]['avg_confidence']
                        current_count = layer_stats[layer_name]['signals']

                        # تحديث المتوسط
                        layer_stats[layer_name]['avg_confidence'] = (
                            (current_avg * (current_count - 1) + confidence) / current_count
                        )

        return layer_stats

    def update_strategy_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """تحديث إعدادات الاستراتيجية"""
        try:
            updated = {}

            if 'min_confidence_threshold' in settings:
                old_value = self.min_confidence_threshold
                self.min_confidence_threshold = max(0, min(100, settings['min_confidence_threshold']))
                updated['min_confidence_threshold'] = {
                    'old': old_value,
                    'new': self.min_confidence_threshold
                }

            if 'min_agreement_layers' in settings:
                old_value = self.min_agreement_layers
                self.min_agreement_layers = max(1, min(4, settings['min_agreement_layers']))
                updated['min_agreement_layers'] = {
                    'old': old_value,
                    'new': self.min_agreement_layers
                }

            if 'risk_management_enabled' in settings:
                old_value = self.risk_management_enabled
                self.risk_management_enabled = bool(settings['risk_management_enabled'])
                updated['risk_management_enabled'] = {
                    'old': old_value,
                    'new': self.risk_management_enabled
                }

            if 'trading_hours' in settings:
                old_value = self.trading_hours.copy()
                new_hours = settings['trading_hours']
                if 'start' in new_hours:
                    self.trading_hours['start'] = max(0, min(23, new_hours['start']))
                if 'end' in new_hours:
                    self.trading_hours['end'] = max(0, min(23, new_hours['end']))
                updated['trading_hours'] = {
                    'old': old_value,
                    'new': self.trading_hours.copy()
                }

            logger.info(f"Strategy settings updated: {updated}")

            return {
                'success': True,
                'updated_settings': updated,
                'current_settings': self.get_current_settings()
            }

        except Exception as e:
            logger.error(f"Error updating strategy settings: {e}")
            return {'success': False, 'error': str(e)}

    def get_current_settings(self) -> Dict[str, Any]:
        """الحصول على الإعدادات الحالية"""
        return {
            'min_confidence_threshold': self.min_confidence_threshold,
            'min_agreement_layers': self.min_agreement_layers,
            'risk_management_enabled': self.risk_management_enabled,
            'trading_hours': self.trading_hours.copy(),
            'available_expiry_times': self.available_expiry_times.copy()
        }

    def run_strategy_analysis(self, pair_name: str, candles: List[Dict[str, Any]],
                            indicators_data: Optional[Dict[str, Any]] = None,
                            pair_data: Optional[Dict[str, List[Dict[str, Any]]]] = None,
                            live_candle: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        تشغيل التحليل الكامل واتخاذ القرار

        Args:
            pair_name: اسم زوج العملات
            candles: قائمة الشموع
            indicators_data: بيانات المؤشرات
            pair_data: بيانات أزواج أخرى
            live_candle: الشمعة الحية الحالية

        Returns:
            التحليل الكامل والقرار النهائي
        """
        try:
            logger.info(f"🚀 Starting complete strategy analysis for {pair_name}")

            # تحليل السوق
            market_analysis = self.analyze_market(pair_name, candles, indicators_data, pair_data, live_candle)

            # اتخاذ القرار
            trading_decision = self.make_trading_decision(market_analysis)

            # دمج النتائج
            complete_analysis = {
                'strategy_name': self.name,
                'pair_name': pair_name,
                'analysis_timestamp': datetime.now().isoformat(),
                'market_analysis': market_analysis,
                'trading_decision': trading_decision,
                'strategy_settings': self.get_current_settings(),
                'performance_summary': self.get_strategy_performance()
            }

            logger.info(f"✅ Strategy analysis completed for {pair_name}")

            return complete_analysis

        except Exception as e:
            logger.error(f"Error in complete strategy analysis: {e}")
            return {
                'error': str(e),
                'pair_name': pair_name,
                'timestamp': datetime.now().isoformat()
            }
