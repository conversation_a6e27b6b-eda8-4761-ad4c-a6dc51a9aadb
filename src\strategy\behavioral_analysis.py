"""
Behavioral Analysis Layer - الطبقة الثالثة
تحليل سلوكي متقدم لنفسية السوق وأنماط السلوك المتكررة
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
import json
import os

logger = logging.getLogger(__name__)

class BehavioralAnalysisLayer:
    """الطبقة الثالثة: التحليل السلوكي"""
    
    def __init__(self):
        """تهيئة طبقة التحليل السلوكي"""
        self.name = "Behavioral Analysis Layer"
        self.weight = 0.25  # وزن هذه الطبقة في القرار النهائي
        
        # إعدادات التحليل السلوكي
        self.fear_greed_window = 20
        self.volume_analysis_window = 50
        self.reversal_pattern_window = 30
        self.sentiment_indicators = ['volume_spikes', 'price_gaps', 'reversal_frequency']
        
        logger.info(f"✅ {self.name} initialized")
    
    def analyze_fear_greed_patterns(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل أنماط الخوف والطمع في السوق
        
        Args:
            candles: قائمة الشموع
            
        Returns:
            نتائج تحليل الخوف والطمع
        """
        try:
            df = pd.DataFrame(candles)
            df['open'] = pd.to_numeric(df['open'])
            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])
            df['close'] = pd.to_numeric(df['close'])
            
            if len(df) < self.fear_greed_window:
                return {"error": "Not enough data for fear/greed analysis"}
            
            # تحليل شموع الخوف
            fear_candles = self._detect_fear_candles(df)
            
            # تحليل شموع الطمع
            greed_candles = self._detect_greed_candles(df)
            
            # تحليل الانعكاسات النفسية
            psychological_reversals = self._detect_psychological_reversals(df)
            
            # حساب مؤشر الخوف والطمع
            fear_greed_index = self._calculate_fear_greed_index(df)
            
            return {
                'fear_candles': fear_candles,
                'greed_candles': greed_candles,
                'psychological_reversals': psychological_reversals,
                'fear_greed_index': fear_greed_index,
                'market_sentiment': self._determine_market_sentiment(fear_greed_index)
            }
            
        except Exception as e:
            logger.error(f"Error in fear/greed analysis: {e}")
            return {"error": str(e)}
    
    def analyze_volume_behavior(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل سلوك الحجم"""
        try:
            df = pd.DataFrame(candles)
            df['close'] = pd.to_numeric(df['close'])
            
            # إذا لم يكن الحجم متاحاً، نستخدم مؤشرات بديلة
            if 'volume' not in df.columns:
                df['volume'] = self._estimate_volume_from_price_action(df)
            else:
                df['volume'] = pd.to_numeric(df['volume'])
            
            if len(df) < self.volume_analysis_window:
                return {"error": "Not enough data for volume analysis"}
            
            # كشف ارتفاعات الحجم
            volume_spikes = self._detect_volume_spikes(df)
            
            # تحليل علاقة السعر بالحجم
            price_volume_relationship = self._analyze_price_volume_relationship(df)
            
            # تحليل توزيع الحجم
            volume_distribution = self._analyze_volume_distribution(df)
            
            # كشف التراكم والتوزيع
            accumulation_distribution = self._detect_accumulation_distribution(df)
            
            return {
                'volume_spikes': volume_spikes,
                'price_volume_relationship': price_volume_relationship,
                'volume_distribution': volume_distribution,
                'accumulation_distribution': accumulation_distribution,
                'volume_trend': self._determine_volume_trend(df)
            }
            
        except Exception as e:
            logger.error(f"Error in volume behavior analysis: {e}")
            return {"error": str(e)}
    
    def analyze_reversal_patterns(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل أنماط الانعكاس المتكررة"""
        try:
            df = pd.DataFrame(candles)
            df['open'] = pd.to_numeric(df['open'])
            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])
            df['close'] = pd.to_numeric(df['close'])
            
            if len(df) < self.reversal_pattern_window:
                return {"error": "Not enough data for reversal pattern analysis"}
            
            # كشف انعكاسات الدعم والمقاومة
            support_resistance_reversals = self._detect_support_resistance_reversals(df)
            
            # تحليل انعكاسات الزخم
            momentum_reversals = self._detect_momentum_reversals(df)
            
            # كشف الانعكاسات الموسمية
            seasonal_reversals = self._detect_seasonal_reversals(df)
            
            # تحليل تكرار الانعكاسات
            reversal_frequency = self._calculate_reversal_frequency(df)
            
            return {
                'support_resistance_reversals': support_resistance_reversals,
                'momentum_reversals': momentum_reversals,
                'seasonal_reversals': seasonal_reversals,
                'reversal_frequency': reversal_frequency,
                'reversal_probability': self._calculate_reversal_probability(df)
            }
            
        except Exception as e:
            logger.error(f"Error in reversal pattern analysis: {e}")
            return {"error": str(e)}
    
    def analyze_market_psychology(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل علم النفس السوقي"""
        try:
            df = pd.DataFrame(candles)
            df['close'] = pd.to_numeric(df['close'])
            
            # تحليل مراحل السوق النفسية
            market_phases = self._identify_market_phases(df)
            
            # تحليل سلوك القطيع
            herd_behavior = self._analyze_herd_behavior(df)
            
            # كشف الذعر والنشوة
            panic_euphoria = self._detect_panic_euphoria(df)
            
            # تحليل مقاومة التغيير
            change_resistance = self._analyze_change_resistance(df)
            
            return {
                'market_phases': market_phases,
                'herd_behavior': herd_behavior,
                'panic_euphoria': panic_euphoria,
                'change_resistance': change_resistance,
                'psychological_pressure': self._calculate_psychological_pressure(df)
            }
            
        except Exception as e:
            logger.error(f"Error in market psychology analysis: {e}")
            return {"error": str(e)}
    
    def generate_behavioral_signal(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """توليد الإشارة السلوكية النهائية"""
        try:
            # تحليل جميع المكونات السلوكية
            fear_greed_analysis = self.analyze_fear_greed_patterns(candles)
            volume_analysis = self.analyze_volume_behavior(candles)
            reversal_analysis = self.analyze_reversal_patterns(candles)
            psychology_analysis = self.analyze_market_psychology(candles)
            
            # حساب النقاط لكل نوع تحليل
            scores = {
                'fear_greed_score': self._calculate_fear_greed_score(fear_greed_analysis),
                'volume_score': self._calculate_volume_score(volume_analysis),
                'reversal_score': self._calculate_reversal_score(reversal_analysis),
                'psychology_score': self._calculate_psychology_score(psychology_analysis)
            }
            
            # حساب النتيجة الإجمالية
            valid_scores = [score for score in scores.values() if score != 0]
            total_score = sum(valid_scores) / len(valid_scores) if valid_scores else 0
            
            # تحديد الاتجاه والقوة
            direction = "CALL" if total_score > 0 else "PUT"
            strength = abs(total_score)
            confidence = min(strength * 100, 100)
            
            # تحديد زمن الصفقة المقترح
            suggested_expiry = self._suggest_expiry_time(strength, confidence, fear_greed_analysis, psychology_analysis)

            return {
                'layer': self.name,
                'weight': self.weight,
                'signal': direction,
                'strength': strength,
                'confidence': confidence,
                'suggested_expiry_minutes': suggested_expiry,
                'scores': scores,
                'total_score': total_score,
                'analysis_details': {
                    'fear_greed': fear_greed_analysis,
                    'volume_behavior': volume_analysis,
                    'reversal_patterns': reversal_analysis,
                    'market_psychology': psychology_analysis
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating behavioral signal: {e}")
            return {
                'layer': self.name,
                'error': str(e),
                'signal': 'NEUTRAL',
                'strength': 0,
                'confidence': 0
            }
    
    # Helper methods
    def _detect_fear_candles(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف شموع الخوف"""
        fear_indicators = []
        
        for i in range(len(df)):
            candle = df.iloc[i]
            
            # شمعة هابطة بجسم كبير
            body_size = abs(candle['close'] - candle['open'])
            candle_range = candle['high'] - candle['low']
            
            if candle_range > 0:
                body_ratio = body_size / candle_range
                
                # شروط شمعة الخوف
                is_fear_candle = (
                    candle['close'] < candle['open'] and  # شمعة هابطة
                    body_ratio > 0.7 and  # جسم كبير
                    candle['low'] == candle['close']  # إغلاق عند أدنى سعر
                )
                
                if is_fear_candle:
                    fear_indicators.append({
                        'index': i,
                        'timestamp': candle.get('time', 0),
                        'intensity': body_ratio,
                        'price_drop': (candle['open'] - candle['close']) / candle['open']
                    })
        
        return {
            'count': len(fear_indicators),
            'recent_fear': len([f for f in fear_indicators if f['index'] >= len(df) - 10]),
            'average_intensity': np.mean([f['intensity'] for f in fear_indicators]) if fear_indicators else 0,
            'fear_candles': fear_indicators[-5:]  # آخر 5 شموع خوف
        }

    def _detect_greed_candles(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف شموع الطمع"""
        greed_indicators = []

        for i in range(len(df)):
            candle = df.iloc[i]

            body_size = abs(candle['close'] - candle['open'])
            candle_range = candle['high'] - candle['low']

            if candle_range > 0:
                body_ratio = body_size / candle_range

                # شروط شمعة الطمع
                is_greed_candle = (
                    candle['close'] > candle['open'] and  # شمعة صاعدة
                    body_ratio > 0.7 and  # جسم كبير
                    candle['high'] == candle['close']  # إغلاق عند أعلى سعر
                )

                if is_greed_candle:
                    greed_indicators.append({
                        'index': i,
                        'timestamp': candle.get('time', 0),
                        'intensity': body_ratio,
                        'price_rise': (candle['close'] - candle['open']) / candle['open']
                    })

        return {
            'count': len(greed_indicators),
            'recent_greed': len([g for g in greed_indicators if g['index'] >= len(df) - 10]),
            'average_intensity': np.mean([g['intensity'] for g in greed_indicators]) if greed_indicators else 0,
            'greed_candles': greed_indicators[-5:]  # آخر 5 شموع طمع
        }

    def _detect_psychological_reversals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف الانعكاسات النفسية"""
        reversals = []

        for i in range(2, len(df) - 2):
            # فحص نمط الانعكاس النفسي
            prev_candle = df.iloc[i-1]
            curr_candle = df.iloc[i]
            next_candle = df.iloc[i+1]

            # انعكاس صاعد بعد خوف
            bullish_reversal = (
                prev_candle['close'] < prev_candle['open'] and  # شمعة هابطة سابقة
                curr_candle['low'] < prev_candle['low'] and  # كسر الدعم
                curr_candle['close'] > curr_candle['open'] and  # شمعة صاعدة حالية
                next_candle['close'] > curr_candle['close']  # استمرار الصعود
            )

            # انعكاس هابط بعد طمع
            bearish_reversal = (
                prev_candle['close'] > prev_candle['open'] and  # شمعة صاعدة سابقة
                curr_candle['high'] > prev_candle['high'] and  # كسر المقاومة
                curr_candle['close'] < curr_candle['open'] and  # شمعة هابطة حالية
                next_candle['close'] < curr_candle['close']  # استمرار الهبوط
            )

            if bullish_reversal:
                reversals.append({
                    'type': 'bullish_psychological',
                    'index': i,
                    'strength': 0.8,
                    'trigger': 'fear_exhaustion'
                })
            elif bearish_reversal:
                reversals.append({
                    'type': 'bearish_psychological',
                    'index': i,
                    'strength': 0.8,
                    'trigger': 'greed_exhaustion'
                })

        return {
            'count': len(reversals),
            'recent_reversals': len([r for r in reversals if r['index'] >= len(df) - 20]),
            'reversal_types': [r['type'] for r in reversals],
            'latest_reversal': reversals[-1] if reversals else None
        }

    def _calculate_fear_greed_index(self, df: pd.DataFrame) -> Dict[str, Any]:
        """حساب مؤشر الخوف والطمع"""
        if len(df) < 10:
            return {'index': 50, 'sentiment': 'NEUTRAL'}

        # حساب مكونات المؤشر
        price_momentum = self._calculate_price_momentum(df)
        volatility_factor = self._calculate_volatility_factor(df)
        volume_factor = self._calculate_volume_factor(df)

        # حساب المؤشر المركب (0-100)
        fear_greed_index = (
            price_momentum * 0.4 +
            volatility_factor * 0.3 +
            volume_factor * 0.3
        )

        # تحديد المشاعر
        if fear_greed_index > 75:
            sentiment = 'EXTREME_GREED'
        elif fear_greed_index > 60:
            sentiment = 'GREED'
        elif fear_greed_index > 40:
            sentiment = 'NEUTRAL'
        elif fear_greed_index > 25:
            sentiment = 'FEAR'
        else:
            sentiment = 'EXTREME_FEAR'

        return {
            'index': fear_greed_index,
            'sentiment': sentiment,
            'components': {
                'price_momentum': price_momentum,
                'volatility_factor': volatility_factor,
                'volume_factor': volume_factor
            }
        }

    def _determine_market_sentiment(self, fear_greed_index: Dict[str, Any]) -> str:
        """تحديد مشاعر السوق"""
        sentiment = fear_greed_index.get('sentiment', 'NEUTRAL')
        index_value = fear_greed_index.get('index', 50)

        # تحديد قوة المشاعر
        if sentiment in ['EXTREME_FEAR', 'EXTREME_GREED']:
            return f"{sentiment}_HIGH_REVERSAL_PROBABILITY"
        elif sentiment in ['FEAR', 'GREED']:
            return f"{sentiment}_MODERATE_REVERSAL_PROBABILITY"
        else:
            return "NEUTRAL_CONTINUE_TREND"

    def _estimate_volume_from_price_action(self, df: pd.DataFrame) -> pd.Series:
        """تقدير الحجم من حركة السعر"""
        # استخدام نطاق السعر كمؤشر للحجم
        price_range = df['high'] - df['low']
        body_size = abs(df['close'] - df['open'])

        # تقدير الحجم بناءً على النشاط السعري
        estimated_volume = (price_range + body_size) * 1000

        return estimated_volume

    def _detect_volume_spikes(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف ارتفاعات الحجم"""
        volume_mean = df['volume'].rolling(window=20).mean()
        volume_std = df['volume'].rolling(window=20).std()

        # تحديد ارتفاعات الحجم (أكثر من 2 انحراف معياري)
        volume_spikes = df['volume'] > (volume_mean + 2 * volume_std)

        spike_indices = df[volume_spikes].index.tolist()

        return {
            'spike_count': len(spike_indices),
            'recent_spikes': len([i for i in spike_indices if i >= len(df) - 10]),
            'spike_intensity': df.loc[spike_indices, 'volume'].mean() if spike_indices else 0,
            'latest_spike_index': spike_indices[-1] if spike_indices else None
        }

    def _analyze_price_volume_relationship(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل علاقة السعر بالحجم"""
        # حساب العوائد
        returns = df['close'].pct_change()
        volume_change = df['volume'].pct_change()

        # حساب الارتباط
        correlation = returns.corr(volume_change)

        # تحليل الاتجاهات
        up_days = returns > 0
        down_days = returns < 0

        avg_volume_up = df.loc[up_days, 'volume'].mean()
        avg_volume_down = df.loc[down_days, 'volume'].mean()

        return {
            'price_volume_correlation': correlation,
            'volume_on_up_days': avg_volume_up,
            'volume_on_down_days': avg_volume_down,
            'volume_bias': 'BULLISH' if avg_volume_up > avg_volume_down else 'BEARISH'
        }

    def _analyze_volume_distribution(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل توزيع الحجم"""
        volume_percentiles = {
            '25th': df['volume'].quantile(0.25),
            '50th': df['volume'].quantile(0.50),
            '75th': df['volume'].quantile(0.75),
            '90th': df['volume'].quantile(0.90)
        }

        current_volume = df['volume'].iloc[-1]

        # تحديد موقع الحجم الحالي
        if current_volume > volume_percentiles['90th']:
            volume_level = 'VERY_HIGH'
        elif current_volume > volume_percentiles['75th']:
            volume_level = 'HIGH'
        elif current_volume > volume_percentiles['50th']:
            volume_level = 'ABOVE_AVERAGE'
        elif current_volume > volume_percentiles['25th']:
            volume_level = 'BELOW_AVERAGE'
        else:
            volume_level = 'LOW'

        return {
            'percentiles': volume_percentiles,
            'current_volume': current_volume,
            'volume_level': volume_level,
            'volume_consistency': df['volume'].std() / df['volume'].mean()
        }

    def _detect_accumulation_distribution(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف التراكم والتوزيع"""
        # حساب مؤشر التراكم/التوزيع
        money_flow_multiplier = ((df['close'] - df['low']) - (df['high'] - df['close'])) / (df['high'] - df['low'])
        money_flow_volume = money_flow_multiplier * df['volume']
        ad_line = money_flow_volume.cumsum()

        # تحليل الاتجاه
        recent_ad_trend = ad_line.tail(10).diff().mean()

        if recent_ad_trend > 0:
            phase = 'ACCUMULATION'
            signal = 'BULLISH'
        elif recent_ad_trend < 0:
            phase = 'DISTRIBUTION'
            signal = 'BEARISH'
        else:
            phase = 'NEUTRAL'
            signal = 'NEUTRAL'

        return {
            'current_ad_value': ad_line.iloc[-1],
            'ad_trend': recent_ad_trend,
            'phase': phase,
            'signal': signal,
            'strength': abs(recent_ad_trend) / ad_line.std() if ad_line.std() != 0 else 0
        }

    def _determine_volume_trend(self, df: pd.DataFrame) -> str:
        """تحديد اتجاه الحجم"""
        volume_sma_short = df['volume'].rolling(window=5).mean()
        volume_sma_long = df['volume'].rolling(window=20).mean()

        if volume_sma_short.iloc[-1] > volume_sma_long.iloc[-1]:
            return 'INCREASING'
        elif volume_sma_short.iloc[-1] < volume_sma_long.iloc[-1]:
            return 'DECREASING'
        else:
            return 'STABLE'

    def _detect_support_resistance_reversals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف انعكاسات الدعم والمقاومة"""
        # تحديد مستويات الدعم والمقاومة
        highs = df['high'].rolling(window=5, center=True).max()
        lows = df['low'].rolling(window=5, center=True).min()

        resistance_levels = df[df['high'] == highs]['high'].unique()
        support_levels = df[df['low'] == lows]['low'].unique()

        # كشف الانعكاسات عند هذه المستويات
        reversals = []
        current_price = df['close'].iloc[-1]

        # فحص المقاومة
        for resistance in resistance_levels[-5:]:  # آخر 5 مستويات
            if abs(current_price - resistance) / resistance < 0.002:  # قريب من المقاومة
                reversals.append({
                    'type': 'resistance_reversal',
                    'level': resistance,
                    'signal': 'BEARISH',
                    'strength': 0.7
                })

        # فحص الدعم
        for support in support_levels[-5:]:  # آخر 5 مستويات
            if abs(current_price - support) / support < 0.002:  # قريب من الدعم
                reversals.append({
                    'type': 'support_reversal',
                    'level': support,
                    'signal': 'BULLISH',
                    'strength': 0.7
                })

        return {
            'resistance_levels': resistance_levels[-3:].tolist(),
            'support_levels': support_levels[-3:].tolist(),
            'active_reversals': reversals,
            'reversal_probability': len(reversals) * 0.3
        }

    def _detect_momentum_reversals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف انعكاسات الزخم"""
        # حساب مؤشر الزخم
        momentum = df['close'] / df['close'].shift(10) - 1

        # كشف التباعد
        price_trend = df['close'].tail(5).diff().mean()
        momentum_trend = momentum.tail(5).diff().mean()

        # تحديد التباعد
        if price_trend > 0 and momentum_trend < 0:
            divergence = 'BEARISH_DIVERGENCE'
            reversal_signal = 'BEARISH'
        elif price_trend < 0 and momentum_trend > 0:
            divergence = 'BULLISH_DIVERGENCE'
            reversal_signal = 'BULLISH'
        else:
            divergence = 'NO_DIVERGENCE'
            reversal_signal = 'NEUTRAL'

        return {
            'current_momentum': momentum.iloc[-1],
            'momentum_trend': momentum_trend,
            'divergence': divergence,
            'reversal_signal': reversal_signal,
            'strength': abs(momentum_trend) if divergence != 'NO_DIVERGENCE' else 0
        }

    def _detect_seasonal_reversals(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف الانعكاسات الموسمية"""
        # تحليل أنماط الوقت (إذا كانت البيانات تحتوي على timestamps)
        if 'time' in df.columns:
            df['datetime'] = pd.to_datetime(df['time'], unit='s')
            df['hour'] = df['datetime'].dt.hour
            df['day_of_week'] = df['datetime'].dt.dayofweek

            # تحليل الأنماط الساعية
            hourly_returns = df.groupby('hour')['close'].pct_change().mean()

            # تحليل أنماط أيام الأسبوع
            daily_returns = df.groupby('day_of_week')['close'].pct_change().mean()

            current_hour = datetime.now().hour
            current_day = datetime.now().weekday()

            return {
                'hourly_pattern': dict(hourly_returns) if hasattr(hourly_returns, 'to_dict') else {},
                'daily_pattern': dict(daily_returns) if hasattr(daily_returns, 'to_dict') else {},
                'current_hour_bias': hourly_returns.get(current_hour, 0) if hasattr(hourly_returns, 'get') else 0,
                'current_day_bias': daily_returns.get(current_day, 0) if hasattr(daily_returns, 'get') else 0,
                'seasonal_signal': 'NEUTRAL'
            }
        else:
            return {'error': 'No timestamp data available for seasonal analysis'}

    def _calculate_reversal_frequency(self, df: pd.DataFrame) -> Dict[str, Any]:
        """حساب تكرار الانعكاسات"""
        # تحديد نقاط الانعكاس
        price_changes = df['close'].pct_change()

        # كشف الانعكاسات (تغيير في الاتجاه)
        reversals = 0
        for i in range(2, len(price_changes)):
            if (price_changes.iloc[i-1] > 0 and price_changes.iloc[i] < 0) or \
               (price_changes.iloc[i-1] < 0 and price_changes.iloc[i] > 0):
                reversals += 1

        reversal_frequency = reversals / len(df) if len(df) > 0 else 0

        return {
            'total_reversals': reversals,
            'reversal_frequency': reversal_frequency,
            'market_volatility': 'HIGH' if reversal_frequency > 0.3 else 'NORMAL',
            'trend_strength': 1 - reversal_frequency  # كلما قل التكرار، زادت قوة الترند
        }

    def _calculate_reversal_probability(self, df: pd.DataFrame) -> float:
        """حساب احتمالية الانعكاس"""
        # عوامل متعددة لحساب احتمالية الانعكاس
        factors = []

        # عامل التقلبات
        volatility = df['close'].pct_change().std()
        if volatility > 0.02:
            factors.append(0.3)  # تقلبات عالية تزيد احتمالية الانعكاس

        # عامل الاتجاه المطول
        trend_length = self._calculate_trend_length(df)
        if trend_length > 20:
            factors.append(0.4)  # اتجاه طويل يزيد احتمالية الانعكاس

        # عامل الحجم
        if 'volume' in df.columns:
            recent_volume = df['volume'].tail(5).mean()
            avg_volume = df['volume'].mean()
            if recent_volume > avg_volume * 1.5:
                factors.append(0.3)  # حجم عالي يزيد احتمالية الانعكاس

        return min(sum(factors), 1.0)

    def _calculate_trend_length(self, df: pd.DataFrame) -> int:
        """حساب طول الاتجاه الحالي"""
        price_changes = df['close'].pct_change()
        current_direction = 1 if price_changes.iloc[-1] > 0 else -1

        length = 1
        for i in range(len(price_changes) - 2, -1, -1):
            change_direction = 1 if price_changes.iloc[i] > 0 else -1
            if change_direction == current_direction:
                length += 1
            else:
                break

        return length

    # Helper methods for market psychology
    def _identify_market_phases(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحديد مراحل السوق النفسية"""
        # حساب المتوسطات المتحركة
        sma_short = df['close'].rolling(window=10).mean()
        sma_long = df['close'].rolling(window=50).mean()

        current_price = df['close'].iloc[-1]
        current_sma_short = sma_short.iloc[-1]
        current_sma_long = sma_long.iloc[-1]

        # تحديد المرحلة
        if current_price > current_sma_short > current_sma_long:
            phase = 'BULL_MARKET'
            psychology = 'OPTIMISM'
        elif current_price < current_sma_short < current_sma_long:
            phase = 'BEAR_MARKET'
            psychology = 'PESSIMISM'
        elif current_price > current_sma_long and current_sma_short < current_sma_long:
            phase = 'CORRECTION'
            psychology = 'UNCERTAINTY'
        else:
            phase = 'CONSOLIDATION'
            psychology = 'INDECISION'

        return {
            'phase': phase,
            'psychology': psychology,
            'trend_strength': abs(current_sma_short - current_sma_long) / current_sma_long
        }

    def _analyze_herd_behavior(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل سلوك القطيع"""
        # تحليل تجمع الحركات السعرية
        price_changes = df['close'].pct_change()

        # حساب تتابع الحركات في نفس الاتجاه
        consecutive_moves = []
        current_streak = 1

        for i in range(1, len(price_changes)):
            if (price_changes.iloc[i] > 0) == (price_changes.iloc[i-1] > 0):
                current_streak += 1
            else:
                consecutive_moves.append(current_streak)
                current_streak = 1

        avg_streak = np.mean(consecutive_moves) if consecutive_moves else 1

        # تحديد قوة سلوك القطيع
        if avg_streak > 5:
            herd_strength = 'STRONG'
        elif avg_streak > 3:
            herd_strength = 'MODERATE'
        else:
            herd_strength = 'WEAK'

        return {
            'average_streak': avg_streak,
            'herd_strength': herd_strength,
            'current_streak': current_streak,
            'contrarian_opportunity': avg_streak > 7  # فرصة للتداول العكسي
        }

    def _detect_panic_euphoria(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف الذعر والنشوة"""
        price_changes = df['close'].pct_change()

        # كشف الذعر (انخفاضات حادة)
        panic_threshold = -0.03  # انخفاض 3% أو أكثر
        panic_events = (price_changes < panic_threshold).sum()

        # كشف النشوة (ارتفاعات حادة)
        euphoria_threshold = 0.03  # ارتفاع 3% أو أكثر
        euphoria_events = (price_changes > euphoria_threshold).sum()

        # تحليل الحالة الحالية
        recent_changes = price_changes.tail(5)
        current_state = 'NORMAL'

        if any(recent_changes < panic_threshold):
            current_state = 'PANIC'
        elif any(recent_changes > euphoria_threshold):
            current_state = 'EUPHORIA'

        return {
            'panic_events': panic_events,
            'euphoria_events': euphoria_events,
            'current_state': current_state,
            'emotional_intensity': max(abs(recent_changes.min()), abs(recent_changes.max()))
        }

    def _analyze_change_resistance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل مقاومة التغيير"""
        # حساب مستويات الأسعار النفسية (أرقام مدورة)
        current_price = df['close'].iloc[-1]

        # العثور على أقرب مستوى نفسي
        psychological_levels = []
        base = int(current_price)

        for level in [base - 0.5, base, base + 0.5, base + 1.0]:
            if abs(current_price - level) / current_price < 0.01:  # ضمن 1%
                psychological_levels.append(level)

        # تحليل المقاومة عند هذه المستويات
        resistance_strength = len(psychological_levels) * 0.3

        return {
            'psychological_levels': psychological_levels,
            'resistance_strength': resistance_strength,
            'near_psychological_level': len(psychological_levels) > 0,
            'breakout_potential': 1 - resistance_strength
        }

    def _calculate_psychological_pressure(self, df: pd.DataFrame) -> float:
        """حساب الضغط النفسي"""
        # عوامل الضغط النفسي
        volatility = df['close'].pct_change().std()
        trend_consistency = self._calculate_trend_consistency(df)
        recent_performance = df['close'].iloc[-1] / df['close'].iloc[-10] - 1

        # حساب الضغط المركب
        pressure = (
            volatility * 2 +  # التقلبات تزيد الضغط
            (1 - trend_consistency) +  # عدم الاتساق يزيد الضغط
            abs(recent_performance) * 0.5  # الأداء المتطرف يزيد الضغط
        )

        return min(pressure, 1.0)

    def _calculate_trend_consistency(self, df: pd.DataFrame) -> float:
        """حساب اتساق الاتجاه"""
        price_changes = df['close'].pct_change().tail(20)

        # حساب نسبة الحركات في نفس اتجاه الترند العام
        overall_trend = 1 if price_changes.sum() > 0 else -1
        consistent_moves = sum(1 for change in price_changes if (change > 0) == (overall_trend > 0))

        return consistent_moves / len(price_changes)

    # Helper methods for calculations
    def _calculate_price_momentum(self, df: pd.DataFrame) -> float:
        """حساب زخم السعر"""
        short_ma = df['close'].rolling(window=5).mean().iloc[-1]
        long_ma = df['close'].rolling(window=20).mean().iloc[-1]

        momentum = (short_ma / long_ma - 1) * 100

        # تحويل إلى مقياس 0-100
        return max(0, min(100, 50 + momentum * 10))

    def _calculate_volatility_factor(self, df: pd.DataFrame) -> float:
        """حساب عامل التقلبات"""
        volatility = df['close'].pct_change().std()

        # تحويل التقلبات إلى مقياس 0-100 (تقلبات عالية = خوف)
        normalized_vol = min(volatility * 1000, 50)

        return 50 - normalized_vol  # كلما زادت التقلبات، قل المؤشر (خوف أكثر)

    def _calculate_volume_factor(self, df: pd.DataFrame) -> float:
        """حساب عامل الحجم"""
        if 'volume' not in df.columns:
            return 50  # محايد إذا لم يكن الحجم متاحاً

        recent_volume = df['volume'].tail(5).mean()
        avg_volume = df['volume'].mean()

        volume_ratio = recent_volume / avg_volume

        # تحويل إلى مقياس 0-100
        return max(0, min(100, 50 + (volume_ratio - 1) * 25))

    # Scoring methods
    def _calculate_fear_greed_score(self, fear_greed_analysis: Dict[str, Any]) -> float:
        """حساب نقاط الخوف والطمع"""
        if 'error' in fear_greed_analysis:
            return 0

        fear_greed_index = fear_greed_analysis.get('fear_greed_index', {})
        sentiment = fear_greed_index.get('sentiment', 'NEUTRAL')
        index_value = fear_greed_index.get('index', 50)

        # تحويل المشاعر إلى نقاط (استراتيجية العكس)
        if sentiment == 'EXTREME_FEAR':
            return 0.8  # فرصة شراء قوية
        elif sentiment == 'FEAR':
            return 0.5  # فرصة شراء متوسطة
        elif sentiment == 'EXTREME_GREED':
            return -0.8  # فرصة بيع قوية
        elif sentiment == 'GREED':
            return -0.5  # فرصة بيع متوسطة
        else:
            return 0  # محايد

    def _calculate_volume_score(self, volume_analysis: Dict[str, Any]) -> float:
        """حساب نقاط تحليل الحجم"""
        if 'error' in volume_analysis:
            return 0

        score = 0

        # نقاط ارتفاعات الحجم
        volume_spikes = volume_analysis.get('volume_spikes', {})
        recent_spikes = volume_spikes.get('recent_spikes', 0)
        if recent_spikes > 2:
            score += 0.3

        # نقاط علاقة السعر بالحجم
        price_volume = volume_analysis.get('price_volume_relationship', {})
        volume_bias = price_volume.get('volume_bias', 'NEUTRAL')
        if volume_bias == 'BULLISH':
            score += 0.3
        elif volume_bias == 'BEARISH':
            score -= 0.3

        # نقاط التراكم/التوزيع
        accumulation = volume_analysis.get('accumulation_distribution', {})
        phase = accumulation.get('phase', 'NEUTRAL')
        if phase == 'ACCUMULATION':
            score += 0.4
        elif phase == 'DISTRIBUTION':
            score -= 0.4

        return max(-1, min(1, score))

    def _calculate_reversal_score(self, reversal_analysis: Dict[str, Any]) -> float:
        """حساب نقاط أنماط الانعكاس"""
        if 'error' in reversal_analysis:
            return 0

        score = 0

        # نقاط انعكاسات الدعم والمقاومة
        support_resistance = reversal_analysis.get('support_resistance_reversals', {})
        reversal_probability = support_resistance.get('reversal_probability', 0)
        score += reversal_probability

        # نقاط انعكاسات الزخم
        momentum_reversals = reversal_analysis.get('momentum_reversals', {})
        reversal_signal = momentum_reversals.get('reversal_signal', 'NEUTRAL')
        strength = momentum_reversals.get('strength', 0)

        if reversal_signal == 'BULLISH':
            score += strength
        elif reversal_signal == 'BEARISH':
            score -= strength

        # نقاط تكرار الانعكاسات
        reversal_freq = reversal_analysis.get('reversal_frequency', {})
        trend_strength = reversal_freq.get('trend_strength', 0.5)
        score += (trend_strength - 0.5) * 0.5  # قوة الترند تؤثر على النقاط

        return max(-1, min(1, score))

    def _calculate_psychology_score(self, psychology_analysis: Dict[str, Any]) -> float:
        """حساب نقاط علم النفس السوقي"""
        if 'error' in psychology_analysis:
            return 0

        score = 0

        # نقاط مراحل السوق
        market_phases = psychology_analysis.get('market_phases', {})
        phase = market_phases.get('phase', 'CONSOLIDATION')
        psychology = market_phases.get('psychology', 'INDECISION')

        if phase == 'BULL_MARKET':
            score += 0.3
        elif phase == 'BEAR_MARKET':
            score -= 0.3
        elif phase == 'CORRECTION':
            score += 0.2  # فرصة شراء في التصحيح

        # نقاط سلوك القطيع
        herd_behavior = psychology_analysis.get('herd_behavior', {})
        contrarian_opportunity = herd_behavior.get('contrarian_opportunity', False)
        if contrarian_opportunity:
            score += 0.4  # فرصة للتداول العكسي

        # نقاط الذعر والنشوة
        panic_euphoria = psychology_analysis.get('panic_euphoria', {})
        current_state = panic_euphoria.get('current_state', 'NORMAL')

        if current_state == 'PANIC':
            score += 0.5  # فرصة شراء في الذعر
        elif current_state == 'EUPHORIA':
            score -= 0.5  # فرصة بيع في النشوة

        # نقاط الضغط النفسي
        psychological_pressure = psychology_analysis.get('psychological_pressure', 0.5)
        if psychological_pressure > 0.7:
            score += 0.3  # ضغط عالي يؤدي للانعكاس

        return max(-1, min(1, score))

    def _suggest_expiry_time(self, strength: float, confidence: float,
                           fear_greed_analysis: Dict[str, Any], psychology_analysis: Dict[str, Any]) -> int:
        """اقتراح زمن انتهاء الصفقة بناءً على التحليل السلوكي"""

        # تحليل مشاعر السوق
        fear_greed_index = fear_greed_analysis.get('fear_greed_index', {})
        sentiment = fear_greed_index.get('sentiment', 'NEUTRAL')

        # تحليل علم النفس السوقي
        market_phases = psychology_analysis.get('market_phases', {})
        phase = market_phases.get('phase', 'CONSOLIDATION')

        panic_euphoria = psychology_analysis.get('panic_euphoria', {})
        current_state = panic_euphoria.get('current_state', 'NORMAL')

        # إشارة قوية جداً مع مشاعر متطرفة
        if confidence >= 90 and strength >= 0.8:
            if sentiment in ['EXTREME_FEAR', 'EXTREME_GREED'] or current_state in ['PANIC', 'EUPHORIA']:
                return 1  # دقيقة واحدة للاستفادة من ردود الفعل السريعة
            else:
                return 2  # دقيقتان

        # إشارة قوية مع مشاعر واضحة
        elif confidence >= 80 and strength >= 0.6:
            if sentiment in ['FEAR', 'GREED'] or phase in ['BULL_MARKET', 'BEAR_MARKET']:
                return 2  # دقيقتان
            else:
                return 3  # ثلاث دقائق

        # إشارة متوسطة
        elif confidence >= 70 and strength >= 0.4:
            if current_state != 'NORMAL':
                return 3  # ثلاث دقائق للتقلبات النفسية
            else:
                return 5  # خمس دقائق

        # إشارة ضعيفة
        else:
            return 5  # خمس دقائق كحد أقصى
