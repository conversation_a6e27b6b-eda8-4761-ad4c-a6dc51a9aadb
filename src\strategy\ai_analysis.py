"""
AI Analysis Layer - الطبقة الرابعة
تحليل الذكاء الاصطناعي والتعلم الآلي للتنبؤ بحركة الأسعار
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
import joblib
import json
import os
import warnings

# إخفاء تحذيرات sklearn المزعجة
warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')

# Machine Learning imports
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report
from sklearn.cluster import KMeans
import xgboost as xgb

logger = logging.getLogger(__name__)

class AIAnalysisLayer:
    """الطبقة الرابعة: تحليل الذكاء الاصطناعي"""
    
    def __init__(self, model_dir: str = "data/ai_models"):
        """تهيئة طبقة الذكاء الاصطناعي"""
        self.name = "AI Analysis Layer"
        self.weight = 0.25  # وزن هذه الطبقة في القرار النهائي
        self.model_dir = model_dir
        
        # إنشاء مجلد النماذج
        os.makedirs(model_dir, exist_ok=True)
        
        # النماذج المختلفة
        self.models = {
            'random_forest': None,
            'xgboost': None,
            'gradient_boosting': None
        }
        
        # معالجات البيانات
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.feature_names = []
        
        # إعدادات التدريب
        self.lookback_period = 20
        self.prediction_horizon = 1
        self.min_training_samples = 100
        
        # تحميل النماذج المدربة إن وجدت
        self._load_models()

        # التدريب التلقائي معطل - يتم التدريب في خيط منفصل
        # استخدم train_ai_models.py للتدريب المنفصل
        
        logger.info(f"✅ {self.name} initialized")
    
    def extract_features(self, candles: List[Dict[str, Any]], 
                        indicators_data: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        استخراج الميزات للتعلم الآلي
        
        Args:
            candles: قائمة الشموع
            indicators_data: بيانات المؤشرات الفنية
            
        Returns:
            DataFrame يحتوي على الميزات المستخرجة
        """
        try:
            df = pd.DataFrame(candles)
            df['open'] = pd.to_numeric(df['open'])
            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])
            df['close'] = pd.to_numeric(df['close'])
            
            # إنشاء DataFrame للميزات بنفس مؤشر البيانات الأصلية
            features = pd.DataFrame(index=df.index)

            # الميزات السعرية الأساسية
            features = self._extract_price_features(df, features)
            
            # الميزات الفنية
            features = self._extract_technical_features(df, features)
            
            # ميزات الحجم (إذا كان متاحاً)
            if 'volume' in df.columns:
                features = self._extract_volume_features(df, features)
            
            # ميزات المؤشرات الفنية
            if indicators_data:
                features = self._extract_indicator_features(indicators_data, features)
            
            # ميزات الوقت
            features = self._extract_time_features(df, features)
            
            # ميزات إحصائية متقدمة
            features = self._extract_statistical_features(df, features)
            
            # تنظيف البيانات
            features = features.fillna(0)
            features = features.replace([np.inf, -np.inf], 0)
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return pd.DataFrame()
    
    def prepare_training_data(self, historical_candles: List[List[Dict[str, Any]]], 
                            historical_outcomes: List[str]) -> Tuple[pd.DataFrame, np.ndarray]:
        """
        تحضير بيانات التدريب
        
        Args:
            historical_candles: قائمة من قوائم الشموع التاريخية
            historical_outcomes: النتائج المقابلة ('CALL', 'PUT', 'NEUTRAL')
            
        Returns:
            ميزات التدريب والنتائج المستهدفة
        """
        try:
            all_features = []
            
            for candles in historical_candles:
                if len(candles) >= self.lookback_period:
                    features = self.extract_features(candles[-self.lookback_period:])
                    if not features.empty:
                        # أخذ آخر صف من الميزات
                        all_features.append(features.iloc[-1].values)
            
            if not all_features:
                return pd.DataFrame(), np.array([])
            
            X = pd.DataFrame(all_features)
            y = np.array(historical_outcomes[:len(all_features)])
            
            # تخزين أسماء الميزات
            self.feature_names = [f"feature_{i}" for i in range(X.shape[1])]
            X.columns = self.feature_names
            
            return X, y
            
        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            return pd.DataFrame(), np.array([])
    
    def train_models(self, X: pd.DataFrame, y: np.ndarray) -> Dict[str, Any]:
        """
        تدريب نماذج التعلم الآلي
        
        Args:
            X: ميزات التدريب
            y: النتائج المستهدفة
            
        Returns:
            نتائج التدريب والتقييم
        """
        try:
            if len(X) < self.min_training_samples:
                return {"error": f"Not enough training samples. Need at least {self.min_training_samples}"}
            
            # تقسيم البيانات
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # تطبيع البيانات
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # ترميز التصنيفات
            y_train_encoded = self.label_encoder.fit_transform(y_train)
            y_test_encoded = self.label_encoder.transform(y_test)
            
            results = {}
            
            # تدريب Random Forest
            rf_results = self._train_random_forest(X_train_scaled, X_test_scaled, 
                                                 y_train_encoded, y_test_encoded)
            results['random_forest'] = rf_results
            
            # تدريب XGBoost
            xgb_results = self._train_xgboost(X_train_scaled, X_test_scaled, 
                                            y_train_encoded, y_test_encoded)
            results['xgboost'] = xgb_results
            
            # تدريب Gradient Boosting
            gb_results = self._train_gradient_boosting(X_train_scaled, X_test_scaled, 
                                                     y_train_encoded, y_test_encoded)
            results['gradient_boosting'] = gb_results
            
            # حفظ النماذج
            self._save_models()
            
            return {
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'feature_count': X.shape[1],
                'model_results': results,
                'best_model': self._select_best_model(results)
            }
            
        except Exception as e:
            logger.error(f"Error training models: {e}")
            return {"error": str(e)}
    
    def predict_direction(self, candles: List[Dict[str, Any]],
                         indicators_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        التنبؤ باتجاه السعر

        Args:
            candles: قائمة الشموع الحالية
            indicators_data: بيانات المؤشرات

        Returns:
            تنبؤات النماذج المختلفة
        """
        try:
            # فحص إذا كانت النماذج مدربة
            models_available = any(model is not None for model in self.models.values())

            if not models_available:
                # استخدام تحليل بسيط بدون نماذج مدربة
                return self._simple_prediction_fallback(candles, indicators_data)

            # استخراج الميزات
            features = self.extract_features(candles, indicators_data)

            if features.empty:
                return {"error": "Could not extract features"}

            # أخذ آخر صف من الميزات
            current_features = features.iloc[-1:].values

            # تطبيع الميزات مع التحقق من عدد الميزات
            if hasattr(self.scaler, 'mean_'):
                # التحقق من تطابق عدد الميزات
                expected_features = len(self.feature_names) if self.feature_names else self.scaler.n_features_in_
                actual_features = current_features.shape[1]

                if actual_features != expected_features:
                    logger.warning(f"Feature mismatch: expected {expected_features}, got {actual_features}")
                    return self._simple_prediction_fallback(candles, indicators_data)

                current_features_scaled = self.scaler.transform(current_features)
            else:
                return self._simple_prediction_fallback(candles, indicators_data)

            predictions = {}
            confidences = {}

            # التنبؤ باستخدام النماذج المختلفة
            for model_name, model in self.models.items():
                if model is not None:
                    try:
                        # التنبؤ
                        pred_encoded = model.predict(current_features_scaled)[0]
                        pred_proba = model.predict_proba(current_features_scaled)[0]

                        # تحويل التنبؤ إلى تصنيف
                        prediction = self.label_encoder.inverse_transform([pred_encoded])[0]
                        confidence = max(pred_proba)

                        predictions[model_name] = prediction
                        confidences[model_name] = confidence

                    except Exception as e:
                        logger.warning(f"Error predicting with {model_name}: {e}")
                        predictions[model_name] = 'NEUTRAL'
                        confidences[model_name] = 0.33

            # حساب التنبؤ المجمع
            ensemble_prediction = self._ensemble_predictions(predictions, confidences)

            return {
                'individual_predictions': predictions,
                'individual_confidences': confidences,
                'ensemble_prediction': ensemble_prediction['prediction'],
                'ensemble_confidence': ensemble_prediction['confidence'],
                'feature_importance': self._get_feature_importance()
            }

        except Exception as e:
            logger.error(f"Error in prediction: {e}")
            return self._simple_prediction_fallback(candles, indicators_data)

    def _simple_prediction_fallback(self, candles: List[Dict[str, Any]],
                                   indicators_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """تنبؤ بسيط بدون نماذج مدربة"""
        try:
            if len(candles) < 5:
                return {
                    'ensemble_prediction': 'NEUTRAL',
                    'ensemble_confidence': 0.33,
                    'fallback_used': True,
                    'reason': 'Insufficient data'
                }

            # تحليل بسيط بناءً على الاتجاه الحالي
            recent_candles = candles[-5:]

            # حساب الاتجاه العام
            price_changes = []
            for i in range(1, len(recent_candles)):
                prev_close = float(recent_candles[i-1]['close'])
                curr_close = float(recent_candles[i]['close'])
                change = (curr_close - prev_close) / prev_close
                price_changes.append(change)

            avg_change = sum(price_changes) / len(price_changes)
            volatility = sum(abs(change) for change in price_changes) / len(price_changes)

            # تحديد الاتجاه
            if avg_change > 0.001:  # ارتفاع أكثر من 0.1%
                prediction = 'CALL'
                confidence = min(0.6, 0.4 + abs(avg_change) * 100)
            elif avg_change < -0.001:  # انخفاض أكثر من 0.1%
                prediction = 'PUT'
                confidence = min(0.6, 0.4 + abs(avg_change) * 100)
            else:
                prediction = 'NEUTRAL'
                confidence = 0.33

            # تقليل الثقة في حالة التقلبات العالية
            if volatility > 0.01:
                confidence *= 0.8

            return {
                'ensemble_prediction': prediction,
                'ensemble_confidence': confidence,
                'fallback_used': True,
                'analysis': {
                    'avg_change': avg_change,
                    'volatility': volatility,
                    'samples': len(price_changes)
                }
            }

        except Exception as e:
            logger.error(f"Error in simple prediction fallback: {e}")
            return {
                'ensemble_prediction': 'NEUTRAL',
                'ensemble_confidence': 0.33,
                'fallback_used': True,
                'error': str(e)
            }

    def perform_clustering_analysis(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل التجميع لتحديد أنماط السوق"""
        try:
            features = self.extract_features(candles)
            
            if len(features) < 10:
                return {"error": "Not enough data for clustering"}
            
            # تطبيع البيانات
            features_scaled = StandardScaler().fit_transform(features)
            
            # تطبيق K-Means
            n_clusters = min(5, len(features) // 3)
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            clusters = kmeans.fit_predict(features_scaled)
            
            # تحليل المجموعات
            cluster_analysis = self._analyze_clusters(features, clusters, candles)
            
            return {
                'n_clusters': n_clusters,
                'current_cluster': clusters[-1],
                'cluster_analysis': cluster_analysis,
                'market_regime': self._determine_market_regime(clusters, features)
            }
            
        except Exception as e:
            logger.error(f"Error in clustering analysis: {e}")
            return {"error": str(e)}

    def generate_ai_signal(self, candles: List[Dict[str, Any]],
                          indicators_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """توليد الإشارة النهائية للذكاء الاصطناعي"""
        try:
            # التنبؤ باتجاه السعر
            direction_prediction = self.predict_direction(candles, indicators_data)

            # تحليل التجميع
            clustering_analysis = self.perform_clustering_analysis(candles)

            # حساب النقاط
            ai_score = self._calculate_ai_score(direction_prediction, clustering_analysis)

            # تحديد الاتجاه والقوة
            if 'error' not in direction_prediction:
                ensemble_prediction = direction_prediction.get('ensemble_prediction', 'NEUTRAL')
                ensemble_confidence = direction_prediction.get('ensemble_confidence', 0)

                direction = ensemble_prediction if ensemble_prediction != 'NEUTRAL' else ('CALL' if ai_score > 0 else 'PUT')
                strength = abs(ai_score)
                confidence = min(ensemble_confidence * 100, 100)
            else:
                direction = 'NEUTRAL'
                strength = 0
                confidence = 0

            # تحديد زمن الصفقة المقترح
            suggested_expiry = self._suggest_expiry_time(strength, confidence, clustering_analysis)

            return {
                'layer': self.name,
                'weight': self.weight,
                'signal': direction,
                'strength': strength,
                'confidence': confidence,
                'suggested_expiry_minutes': suggested_expiry,
                'ai_score': ai_score,
                'analysis_details': {
                    'direction_prediction': direction_prediction,
                    'clustering_analysis': clustering_analysis
                },
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating AI signal: {e}")
            return {
                'layer': self.name,
                'error': str(e),
                'signal': 'NEUTRAL',
                'strength': 0,
                'confidence': 0
            }

    # Helper methods for feature extraction
    def _extract_price_features(self, df: pd.DataFrame, features: pd.DataFrame) -> pd.DataFrame:
        """استخراج الميزات السعرية"""
        try:
            # العوائد
            features.loc[:, 'returns_1'] = df['close'].pct_change(1)
            features.loc[:, 'returns_5'] = df['close'].pct_change(5)
            features.loc[:, 'returns_10'] = df['close'].pct_change(10)

            # نسب الأسعار (مع حماية من القسمة على صفر)
            features.loc[:, 'high_low_ratio'] = df['high'] / df['low'].replace(0, 0.00001)
            features.loc[:, 'close_open_ratio'] = df['close'] / df['open'].replace(0, 0.00001)

            # حماية من القسمة على صفر
            range_val = (df['high'] - df['low']).replace(0, 0.00001)
            features.loc[:, 'body_to_range_ratio'] = abs(df['close'] - df['open']) / range_val

            # مستويات الأسعار النسبية
            features.loc[:, 'price_position'] = (df['close'] - df['low']) / range_val

            return features
        except Exception as e:
            print(f"Error in _extract_price_features: {e}")
            # إرجاع DataFrame فارغ في حالة الخطأ
            return pd.DataFrame(index=df.index)

    def _extract_technical_features(self, df: pd.DataFrame, features: pd.DataFrame) -> pd.DataFrame:
        """استخراج الميزات الفنية"""
        try:
            # المتوسطات المتحركة
            for period in [5, 10, 20]:
                sma = df['close'].rolling(window=period).mean()
                features.loc[:, f'sma_{period}_ratio'] = df['close'] / sma.replace(0, 0.00001)
                features.loc[:, f'sma_{period}_slope'] = sma.diff(5)

            # RSI مبسط
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss.replace(0, 0.00001)
            features.loc[:, 'rsi'] = 100 - (100 / (1 + rs))

            # Bollinger Bands
            sma_20 = df['close'].rolling(window=20).mean()
            std_20 = df['close'].rolling(window=20).std()
            features.loc[:, 'bb_upper'] = (sma_20 + 2 * std_20) / df['close'].replace(0, 0.00001)
            features.loc[:, 'bb_lower'] = (sma_20 - 2 * std_20) / df['close'].replace(0, 0.00001)
            features.loc[:, 'bb_position'] = (df['close'] - (sma_20 - 2 * std_20)) / (4 * std_20).replace(0, 0.00001)

            return features
        except Exception as e:
            print(f"Error in _extract_technical_features: {e}")
            return features

    def _extract_volume_features(self, df: pd.DataFrame, features: pd.DataFrame) -> pd.DataFrame:
        """استخراج ميزات الحجم"""
        try:
            df['volume'] = pd.to_numeric(df['volume'])

            # نسب الحجم
            volume_sma = df['volume'].rolling(window=20).mean()
            features.loc[:, 'volume_ratio'] = df['volume'] / volume_sma.replace(0, 0.00001)
            features.loc[:, 'volume_trend'] = df['volume'].rolling(window=5).mean() / df['volume'].rolling(window=20).mean().replace(0, 0.00001)

            # علاقة السعر بالحجم
            features.loc[:, 'price_volume'] = df['close'].pct_change() * df['volume']

            return features
        except Exception as e:
            print(f"Error in _extract_volume_features: {e}")
            return features

    def _extract_indicator_features(self, indicators_data: Dict[str, Any], features: pd.DataFrame) -> pd.DataFrame:
        """استخراج ميزات من المؤشرات الموجودة"""
        try:
            # استخراج قيم المؤشرات إذا كانت متاحة
            for indicator_name, indicator_data in indicators_data.items():
                if isinstance(indicator_data, dict) and 'current' in indicator_data:
                    # إضافة قيمة ثابتة لجميع الصفوف
                    features.loc[:, f'indicator_{indicator_name}'] = indicator_data['current']
                elif isinstance(indicator_data, (int, float)):
                    features.loc[:, f'indicator_{indicator_name}'] = indicator_data
        except Exception as e:
            logger.warning(f"Error extracting indicator features: {e}")

        return features

    def _extract_time_features(self, df: pd.DataFrame, features: pd.DataFrame) -> pd.DataFrame:
        """استخراج ميزات الوقت"""
        try:
            if 'time' in df.columns:
                df['datetime'] = pd.to_datetime(df['time'], unit='s')
                features.loc[:, 'hour'] = df['datetime'].dt.hour
                features.loc[:, 'day_of_week'] = df['datetime'].dt.dayofweek
                features.loc[:, 'is_weekend'] = (df['datetime'].dt.dayofweek >= 5).astype(int)
            else:
                # استخدام الوقت الحالي
                now = datetime.now()
                features.loc[:, 'hour'] = now.hour
                features.loc[:, 'day_of_week'] = now.weekday()
                features.loc[:, 'is_weekend'] = int(now.weekday() >= 5)

            return features
        except Exception as e:
            print(f"Error in _extract_time_features: {e}")
            return features

    def _extract_statistical_features(self, df: pd.DataFrame, features: pd.DataFrame) -> pd.DataFrame:
        """استخراج الميزات الإحصائية"""
        try:
            # التقلبات
            features.loc[:, 'volatility_5'] = df['close'].pct_change().rolling(window=5).std()
            features.loc[:, 'volatility_20'] = df['close'].pct_change().rolling(window=20).std()

            # الانحراف والتفلطح
            returns = df['close'].pct_change()
            features.loc[:, 'skewness'] = returns.rolling(window=20).skew()
            features.loc[:, 'kurtosis'] = returns.rolling(window=20).kurt()

            # Z-Score
            rolling_mean = df['close'].rolling(window=20).mean()
            rolling_std = df['close'].rolling(window=20).std()
            features.loc[:, 'zscore'] = (df['close'] - rolling_mean) / rolling_std.replace(0, 0.00001)

            return features
        except Exception as e:
            print(f"Error in _extract_statistical_features: {e}")
            return features

    # Model training methods
    def _train_random_forest(self, X_train: np.ndarray, X_test: np.ndarray,
                           y_train: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
        """تدريب نموذج Random Forest"""
        try:
            rf = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )

            rf.fit(X_train, y_train)

            # التنبؤ والتقييم
            y_pred = rf.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)

            # Cross-validation
            cv_scores = cross_val_score(rf, X_train, y_train, cv=5)

            self.models['random_forest'] = rf

            return {
                'accuracy': accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'feature_importance': rf.feature_importances_.tolist()
            }

        except Exception as e:
            logger.error(f"Error training Random Forest: {e}")
            return {'error': str(e)}

    def _train_xgboost(self, X_train: np.ndarray, X_test: np.ndarray,
                      y_train: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
        """تدريب نموذج XGBoost"""
        try:
            xgb_model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                eval_metric='mlogloss'
            )

            xgb_model.fit(X_train, y_train)

            # التنبؤ والتقييم
            y_pred = xgb_model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)

            # Cross-validation
            cv_scores = cross_val_score(xgb_model, X_train, y_train, cv=5)

            self.models['xgboost'] = xgb_model

            return {
                'accuracy': accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'feature_importance': xgb_model.feature_importances_.tolist()
            }

        except Exception as e:
            logger.error(f"Error training XGBoost: {e}")
            return {'error': str(e)}

    def _train_gradient_boosting(self, X_train: np.ndarray, X_test: np.ndarray,
                               y_train: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
        """تدريب نموذج Gradient Boosting"""
        try:
            gb = GradientBoostingClassifier(
                n_estimators=100,
                max_depth=5,
                learning_rate=0.1,
                random_state=42
            )

            gb.fit(X_train, y_train)

            # التنبؤ والتقييم
            y_pred = gb.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)

            # Cross-validation
            cv_scores = cross_val_score(gb, X_train, y_train, cv=5)

            self.models['gradient_boosting'] = gb

            return {
                'accuracy': accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'feature_importance': gb.feature_importances_.tolist()
            }

        except Exception as e:
            logger.error(f"Error training Gradient Boosting: {e}")
            return {'error': str(e)}

    def _select_best_model(self, results: Dict[str, Any]) -> str:
        """اختيار أفضل نموذج"""
        best_model = 'random_forest'
        best_score = 0

        for model_name, model_results in results.items():
            if 'error' not in model_results:
                cv_mean = model_results.get('cv_mean', 0)
                if cv_mean > best_score:
                    best_score = cv_mean
                    best_model = model_name

        return best_model

    def _ensemble_predictions(self, predictions: Dict[str, str],
                            confidences: Dict[str, float]) -> Dict[str, Any]:
        """دمج تنبؤات النماذج المختلفة"""
        if not predictions:
            return {'prediction': 'NEUTRAL', 'confidence': 0.33}

        # حساب الأوزان بناءً على الثقة
        weighted_votes = {}
        total_weight = 0

        for model_name, prediction in predictions.items():
            confidence = confidences.get(model_name, 0.33)

            if prediction not in weighted_votes:
                weighted_votes[prediction] = 0

            weighted_votes[prediction] += confidence
            total_weight += confidence

        # تطبيع الأوزان
        if total_weight > 0:
            for prediction in weighted_votes:
                weighted_votes[prediction] /= total_weight

        # اختيار التنبؤ الأعلى وزناً
        best_prediction = max(weighted_votes, key=weighted_votes.get)
        best_confidence = weighted_votes[best_prediction]

        return {
            'prediction': best_prediction,
            'confidence': best_confidence,
            'all_votes': weighted_votes
        }

    def _get_feature_importance(self) -> Dict[str, Any]:
        """الحصول على أهمية الميزات"""
        importance_data = {}

        for model_name, model in self.models.items():
            if model is not None and hasattr(model, 'feature_importances_'):
                importance_data[model_name] = model.feature_importances_.tolist()

        return importance_data

    def _analyze_clusters(self, features: pd.DataFrame, clusters: np.ndarray,
                         candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل المجموعات"""
        cluster_info = {}

        for cluster_id in np.unique(clusters):
            cluster_mask = clusters == cluster_id
            cluster_features = features[cluster_mask]
            cluster_candles = [candles[i] for i in range(len(candles)) if cluster_mask[i]]

            if len(cluster_candles) > 0:
                # حساب خصائص المجموعة
                cluster_returns = []
                for candle in cluster_candles:
                    if isinstance(candle, dict):
                        open_price = float(candle.get('open', 0))
                        close_price = float(candle.get('close', 0))
                        if open_price > 0:
                            cluster_returns.append((close_price - open_price) / open_price)

                cluster_info[f'cluster_{cluster_id}'] = {
                    'size': len(cluster_candles),
                    'avg_return': np.mean(cluster_returns) if cluster_returns else 0,
                    'volatility': np.std(cluster_returns) if cluster_returns else 0,
                    'characteristics': self._describe_cluster_characteristics(cluster_features)
                }

        return cluster_info

    def _describe_cluster_characteristics(self, cluster_features: pd.DataFrame) -> Dict[str, str]:
        """وصف خصائص المجموعة"""
        characteristics = {}

        # تحليل الميزات الرئيسية
        feature_means = cluster_features.mean()

        # تحديد الخصائص البارزة
        if 'volatility_20' in feature_means.index:
            vol = feature_means['volatility_20']
            if vol > 0.02:
                characteristics['volatility'] = 'HIGH'
            elif vol < 0.01:
                characteristics['volatility'] = 'LOW'
            else:
                characteristics['volatility'] = 'NORMAL'

        if 'rsi' in feature_means.index:
            rsi = feature_means['rsi']
            if rsi > 70:
                characteristics['momentum'] = 'OVERBOUGHT'
            elif rsi < 30:
                characteristics['momentum'] = 'OVERSOLD'
            else:
                characteristics['momentum'] = 'NEUTRAL'

        return characteristics

    def _determine_market_regime(self, clusters: np.ndarray, features: pd.DataFrame) -> str:
        """تحديد نظام السوق الحالي"""
        current_cluster = clusters[-1]

        # تحليل خصائص المجموعة الحالية
        cluster_mask = clusters == current_cluster
        cluster_features = features[cluster_mask]

        if len(cluster_features) == 0:
            return 'UNKNOWN'

        # حساب الخصائص المتوسطة للمجموعة
        avg_volatility = cluster_features.get('volatility_20', pd.Series([0.01])).mean()
        avg_returns = cluster_features.get('returns_5', pd.Series([0])).mean()

        # تحديد النظام
        if avg_volatility > 0.025:
            if avg_returns > 0.01:
                return 'BULL_VOLATILE'
            elif avg_returns < -0.01:
                return 'BEAR_VOLATILE'
            else:
                return 'SIDEWAYS_VOLATILE'
        else:
            if avg_returns > 0.005:
                return 'BULL_STABLE'
            elif avg_returns < -0.005:
                return 'BEAR_STABLE'
            else:
                return 'SIDEWAYS_STABLE'

    def _calculate_ai_score(self, direction_prediction: Dict[str, Any],
                           clustering_analysis: Dict[str, Any]) -> float:
        """حساب نقاط الذكاء الاصطناعي"""
        score = 0

        # نقاط التنبؤ بالاتجاه
        if 'error' not in direction_prediction:
            ensemble_prediction = direction_prediction.get('ensemble_prediction', 'NEUTRAL')
            ensemble_confidence = direction_prediction.get('ensemble_confidence', 0)

            if ensemble_prediction == 'CALL':
                score += ensemble_confidence
            elif ensemble_prediction == 'PUT':
                score -= ensemble_confidence

        # نقاط تحليل التجميع
        if 'error' not in clustering_analysis:
            market_regime = clustering_analysis.get('market_regime', 'UNKNOWN')

            if 'BULL' in market_regime:
                score += 0.2
            elif 'BEAR' in market_regime:
                score -= 0.2

            # تعديل حسب التقلبات
            if 'VOLATILE' in market_regime:
                score *= 0.8  # تقليل الثقة في الأسواق المتقلبة

        return max(-1, min(1, score))

    # Model persistence methods
    def _save_models(self):
        """حفظ النماذج المدربة"""
        try:
            for model_name, model in self.models.items():
                if model is not None:
                    model_path = os.path.join(self.model_dir, f"{model_name}.joblib")
                    joblib.dump(model, model_path)

            # حفظ معالجات البيانات
            scaler_path = os.path.join(self.model_dir, "scaler.joblib")
            joblib.dump(self.scaler, scaler_path)

            encoder_path = os.path.join(self.model_dir, "label_encoder.joblib")
            joblib.dump(self.label_encoder, encoder_path)

            # حفظ أسماء الميزات
            features_path = os.path.join(self.model_dir, "feature_names.joblib")
            joblib.dump(self.feature_names, features_path)

            logger.info("Models saved successfully")

        except Exception as e:
            logger.error(f"Error saving models: {e}")

    def _load_models(self):
        """تحميل النماذج المدربة"""
        try:
            # تحميل النماذج
            for model_name in self.models.keys():
                model_path = os.path.join(self.model_dir, f"{model_name}.joblib")
                if os.path.exists(model_path):
                    self.models[model_name] = joblib.load(model_path)
                    logger.info(f"Loaded {model_name} model")

            # تحميل معالجات البيانات
            scaler_path = os.path.join(self.model_dir, "scaler.joblib")
            if os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
                logger.info("Loaded scaler")

            encoder_path = os.path.join(self.model_dir, "label_encoder.joblib")
            if os.path.exists(encoder_path):
                self.label_encoder = joblib.load(encoder_path)
                logger.info("Loaded label encoder")

            # تحميل أسماء الميزات
            features_path = os.path.join(self.model_dir, "feature_names.joblib")
            if os.path.exists(features_path):
                self.feature_names = joblib.load(features_path)
                logger.info("Loaded feature names")

        except Exception as e:
            logger.warning(f"Could not load some models: {e}")

    def retrain_with_new_data(self, new_candles: List[List[Dict[str, Any]]],
                             new_outcomes: List[str]) -> Dict[str, Any]:
        """إعادة تدريب النماذج ببيانات جديدة"""
        try:
            # تحضير البيانات الجديدة
            X_new, y_new = self.prepare_training_data(new_candles, new_outcomes)

            if len(X_new) < 10:
                return {"error": "Not enough new data for retraining"}

            # دمج مع البيانات الموجودة إذا كانت متاحة
            # (يمكن تحسين هذا بحفظ البيانات التاريخية)

            # إعادة التدريب
            training_results = self.train_models(X_new, y_new)

            return {
                'retrained': True,
                'new_samples': len(X_new),
                'training_results': training_results
            }

        except Exception as e:
            logger.error(f"Error retraining models: {e}")
            return {"error": str(e)}

    def get_model_performance(self) -> Dict[str, Any]:
        """الحصول على أداء النماذج"""
        performance = {}

        for model_name, model in self.models.items():
            if model is not None:
                performance[model_name] = {
                    'loaded': True,
                    'type': type(model).__name__
                }
            else:
                performance[model_name] = {
                    'loaded': False,
                    'type': None
                }

        performance['scaler_fitted'] = hasattr(self.scaler, 'mean_')
        performance['encoder_fitted'] = hasattr(self.label_encoder, 'classes_')
        performance['feature_count'] = len(self.feature_names)

        return performance

    def _auto_train_models(self):
        """تدريب تلقائي للنماذج إذا لم تكن موجودة"""
        try:
            # فحص إذا كانت النماذج موجودة
            models_exist = any(model is not None for model in self.models.values())

            if not models_exist:
                logger.info("🤖 No trained models found, starting auto-training...")

                # تحميل البيانات التاريخية للتدريب
                training_data = self._load_historical_training_data()

                if training_data and len(training_data['candles']) >= self.min_training_samples:
                    logger.info(f"📚 Training with {len(training_data['candles'])} historical samples")

                    # تحضير بيانات التدريب
                    X, y = self.prepare_training_data(
                        training_data['candles'],
                        training_data['outcomes']
                    )

                    if len(X) >= self.min_training_samples:
                        # تدريب النماذج
                        training_results = self.train_models(X, y)

                        if 'error' not in training_results:
                            logger.info("✅ Auto-training completed successfully")
                        else:
                            logger.warning(f"⚠️ Auto-training failed: {training_results['error']}")
                    else:
                        logger.warning("⚠️ Not enough prepared training data")
                else:
                    logger.warning("⚠️ No historical training data available")
            else:
                logger.info("✅ Pre-trained models loaded successfully")

        except Exception as e:
            logger.error(f"Error in auto-training: {e}")

    def _load_historical_training_data(self) -> Optional[Dict[str, Any]]:
        """تحميل البيانات التاريخية للتدريب"""
        try:
            # البحث عن ملفات البيانات التاريخية
            historical_dir = "data/historical"

            if not os.path.exists(historical_dir):
                return None

            all_candles = []
            all_outcomes = []

            # تحميل البيانات من جميع الأزواج
            for filename in os.listdir(historical_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(historical_dir, filename)

                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        candles = data.get('candles', [])

                        if len(candles) >= 50:
                            # تقسيم البيانات إلى عينات تدريب
                            for i in range(self.lookback_period, len(candles) - self.prediction_horizon):
                                # أخذ آخر lookback_period شمعة
                                sample_candles = candles[i-self.lookback_period:i]

                                # تحديد النتيجة بناءً على الشمعة التالية
                                current_close = candles[i]['close']
                                future_close = candles[i + self.prediction_horizon]['close']

                                if future_close > current_close * 1.001:  # ارتفاع 0.1% أو أكثر
                                    outcome = 'CALL'
                                elif future_close < current_close * 0.999:  # انخفاض 0.1% أو أكثر
                                    outcome = 'PUT'
                                else:
                                    outcome = 'NEUTRAL'

                                all_candles.append(sample_candles)
                                all_outcomes.append(outcome)

                    except Exception as e:
                        logger.warning(f"Could not load training data from {filename}: {e}")

            if len(all_candles) >= self.min_training_samples:
                logger.info(f"📊 Loaded {len(all_candles)} training samples from historical data")
                return {
                    'candles': all_candles,
                    'outcomes': all_outcomes
                }
            else:
                logger.warning(f"Not enough training samples: {len(all_candles)} < {self.min_training_samples}")
                return None

        except Exception as e:
            logger.error(f"Error loading historical training data: {e}")
            return None

    def _suggest_expiry_time(self, strength: float, confidence: float, clustering_analysis: Dict[str, Any]) -> int:
        """اقتراح زمن انتهاء الصفقة بناءً على تحليل الذكاء الاصطناعي"""

        # تحليل نظام السوق من التجميع
        market_regime = clustering_analysis.get('market_regime', 'UNKNOWN')

        # إشارة قوية جداً من الذكاء الاصطناعي
        if confidence >= 95 and strength >= 0.9:
            if 'VOLATILE' in market_regime:
                return 1  # دقيقة واحدة للأسواق المتقلبة
            else:
                return 2  # دقيقتان للأسواق المستقرة

        # إشارة قوية
        elif confidence >= 85 and strength >= 0.7:
            if 'VOLATILE' in market_regime:
                return 2  # دقيقتان
            elif 'STABLE' in market_regime:
                return 3  # ثلاث دقائق للاستقرار
            else:
                return 2  # دقيقتان افتراضي

        # إشارة متوسطة
        elif confidence >= 75 and strength >= 0.5:
            if 'BULL' in market_regime or 'BEAR' in market_regime:
                return 3  # ثلاث دقائق للاتجاهات الواضحة
            else:
                return 5  # خمس دقائق للأسواق الجانبية

        # إشارة ضعيفة
        else:
            return 5  # خمس دقائق كحد أقصى
