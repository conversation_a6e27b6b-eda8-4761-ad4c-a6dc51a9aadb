"""
Risk Management System - نظام إدارة المخاطر
نظام متقدم لإدارة المخاطر في التداول الآلي للخيارات الثنائية
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class RiskManager:
    """مدير المخاطر المتقدم"""
    
    def __init__(self, config_file: str = "risk_config.json"):
        """تهيئة مدير المخاطر"""
        self.config_file = config_file
        self.config = self._load_default_config()
        
        # تحميل الإعدادات المخصصة إن وجدت
        if os.path.exists(config_file):
            self._load_config(config_file)
        
        # تخزين بيانات الجلسة الحالية
        self.current_session = {
            'start_time': None,
            'initial_balance': 0,
            'current_balance': 0,
            'trades_count': 0,
            'wins': 0,
            'losses': 0,
            'total_invested': 0,
            'total_profit': 0,
            'consecutive_losses': 0,
            'max_consecutive_losses': 0,
            'last_trade_time': None
        }
        
        logger.info("✅ Risk Manager initialized")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """تحميل إعدادات إدارة المخاطر الافتراضية"""
        return {
            # إعدادات عامة محسنة لتحقيق 80% نجاح
            "max_risk_per_trade_percent": 2.0,  # 2% من الرصيد لكل صفقة
            "max_daily_loss_percent": 8.0,      # 8% خسارة يومية قصوى (أكثر صرامة)
            "max_session_loss_percent": 10.0,   # 10% خسارة في الجلسة قصوى (أكثر صرامة)
            "max_consecutive_losses": 2,        # 2 خسائر متتالية قصوى (أكثر صرامة)
            "min_balance_required": 100.0,      # الحد الأدنى للرصيد
            
            # إعدادات الجلسة
            "max_trades_per_session": 20,       # عدد الصفقات القصوى في الجلسة
            "max_trades_per_hour": 6,           # عدد الصفقات القصوى في الساعة
            "min_time_between_trades": 60,      # الحد الأدنى بين الصفقات (ثانية)
            "session_timeout_hours": 8,         # انتهاء الجلسة بعد 8 ساعات
            
            # إعدادات التداول المحسنة
            "min_confidence_required": 85,      # الحد الأدنى للثقة (أعلى للجودة)
            "min_agreement_layers": 3,          # الحد الأدنى لاتفاق الطبقات (3 من 4)
            "max_trade_amount": 1000.0,         # الحد الأقصى لمبلغ الصفقة
            "min_trade_amount": 10.0,           # الحد الأدنى لمبلغ الصفقة
            "required_win_rate": 80.0,          # نسبة النجاح المطلوبة
            
            # إعدادات الطوارئ
            "emergency_stop_loss_percent": 20.0,  # إيقاف طارئ عند خسارة 20%
            "circuit_breaker_enabled": True,      # تفعيل قاطع الدائرة
            "cooling_period_minutes": 30,         # فترة تهدئة بعد الخسائر المتتالية
            
            # إعدادات التنبيهات
            "alert_on_high_risk": True,
            "alert_on_consecutive_losses": True,
            "alert_on_daily_loss_limit": True
        }
    
    def _load_config(self, config_file: str):
        """تحميل إعدادات مخصصة"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                custom_config = json.load(f)
            self.config.update(custom_config)
            logger.info(f"Risk management config loaded from {config_file}")
        except Exception as e:
            logger.error(f"Error loading risk config: {e}")
    
    def start_session(self, initial_balance: float, account_type: str = "PRACTICE") -> Dict[str, Any]:
        """بدء جلسة تداول جديدة"""
        try:
            self.current_session = {
                'start_time': datetime.now(),
                'initial_balance': initial_balance,
                'current_balance': initial_balance,
                'account_type': account_type,
                'trades_count': 0,
                'wins': 0,
                'losses': 0,
                'total_invested': 0,
                'total_profit': 0,
                'consecutive_losses': 0,
                'max_consecutive_losses': 0,
                'last_trade_time': None,
                'session_id': f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }
            
            logger.info(f"🚀 Trading session started - Balance: {initial_balance}, Account: {account_type}")
            
            return {
                'status': 'SUCCESS',
                'session_id': self.current_session['session_id'],
                'initial_balance': initial_balance,
                'account_type': account_type,
                'risk_limits': self._get_current_risk_limits()
            }
            
        except Exception as e:
            logger.error(f"Error starting session: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def validate_trade(self, trade_amount: float, confidence: float, 
                      agreement_layers: int, current_balance: float) -> Dict[str, Any]:
        """التحقق من صحة الصفقة قبل التنفيذ"""
        try:
            validation_result = {
                'allowed': False,
                'reasons': [],
                'warnings': [],
                'recommended_amount': trade_amount
            }
            
            # تحديث الرصيد الحالي
            self.current_session['current_balance'] = current_balance
            
            # فحص الحد الأدنى للرصيد
            if current_balance < self.config['min_balance_required']:
                validation_result['reasons'].append(f"Balance too low: {current_balance} < {self.config['min_balance_required']}")
                return validation_result
            
            # فحص مبلغ الصفقة
            if trade_amount < self.config['min_trade_amount']:
                validation_result['reasons'].append(f"Trade amount too low: {trade_amount} < {self.config['min_trade_amount']}")
                return validation_result
            
            if trade_amount > self.config['max_trade_amount']:
                validation_result['reasons'].append(f"Trade amount too high: {trade_amount} > {self.config['max_trade_amount']}")
                return validation_result
            
            # فحص نسبة المخاطرة
            risk_percent = (trade_amount / current_balance) * 100
            max_risk = self.config['max_risk_per_trade_percent']
            
            if risk_percent > max_risk:
                recommended_amount = (current_balance * max_risk) / 100
                validation_result['recommended_amount'] = recommended_amount
                validation_result['reasons'].append(f"Risk too high: {risk_percent:.1f}% > {max_risk}%. Recommended: {recommended_amount:.2f}")
                return validation_result
            
            # فحص مستوى الثقة
            if confidence < self.config['min_confidence_required']:
                validation_result['reasons'].append(f"Confidence too low: {confidence}% < {self.config['min_confidence_required']}%")
                return validation_result
            
            # فحص اتفاق الطبقات
            if agreement_layers < self.config['min_agreement_layers']:
                validation_result['reasons'].append(f"Insufficient layer agreement: {agreement_layers} < {self.config['min_agreement_layers']}")
                return validation_result
            
            # فحص عدد الصفقات في الجلسة
            if self.current_session['trades_count'] >= self.config['max_trades_per_session']:
                validation_result['reasons'].append(f"Max trades per session reached: {self.current_session['trades_count']}")
                return validation_result
            
            # فحص الخسائر المتتالية
            if self.current_session['consecutive_losses'] >= self.config['max_consecutive_losses']:
                validation_result['reasons'].append(f"Max consecutive losses reached: {self.current_session['consecutive_losses']}")
                return validation_result
            
            # فحص خسارة الجلسة
            session_loss_percent = self._calculate_session_loss_percent()
            if session_loss_percent >= self.config['max_session_loss_percent']:
                validation_result['reasons'].append(f"Max session loss reached: {session_loss_percent:.1f}%")
                return validation_result
            
            # فحص الوقت بين الصفقات
            if self.current_session['last_trade_time']:
                time_since_last = (datetime.now() - self.current_session['last_trade_time']).total_seconds()
                if time_since_last < self.config['min_time_between_trades']:
                    validation_result['reasons'].append(f"Too soon since last trade: {time_since_last:.0f}s < {self.config['min_time_between_trades']}s")
                    return validation_result
            
            # فحص عدد الصفقات في الساعة
            trades_last_hour = self._count_trades_last_hour()
            if trades_last_hour >= self.config['max_trades_per_hour']:
                validation_result['reasons'].append(f"Max trades per hour reached: {trades_last_hour}")
                return validation_result
            
            # إضافة تحذيرات
            if risk_percent > max_risk * 0.8:
                validation_result['warnings'].append(f"High risk: {risk_percent:.1f}%")
            
            if self.current_session['consecutive_losses'] >= 2:
                validation_result['warnings'].append(f"Consecutive losses: {self.current_session['consecutive_losses']}")
            
            # الصفقة مسموحة
            validation_result['allowed'] = True
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating trade: {e}")
            return {
                'allowed': False,
                'reasons': [f"Validation error: {str(e)}"],
                'warnings': [],
                'recommended_amount': trade_amount
            }
    
    def record_trade_result(self, trade_amount: float, result: str, profit: float = 0) -> Dict[str, Any]:
        """تسجيل نتيجة الصفقة"""
        try:
            self.current_session['trades_count'] += 1
            self.current_session['total_invested'] += trade_amount
            self.current_session['last_trade_time'] = datetime.now()
            
            if result.upper() == 'WIN':
                self.current_session['wins'] += 1
                self.current_session['total_profit'] += profit
                self.current_session['current_balance'] += profit
                self.current_session['consecutive_losses'] = 0
                
            elif result.upper() == 'LOSS':
                self.current_session['losses'] += 1
                self.current_session['total_profit'] -= trade_amount
                self.current_session['current_balance'] -= trade_amount
                self.current_session['consecutive_losses'] += 1
                
                # تحديث أقصى خسائر متتالية
                if self.current_session['consecutive_losses'] > self.current_session['max_consecutive_losses']:
                    self.current_session['max_consecutive_losses'] = self.current_session['consecutive_losses']
            
            # فحص حالات الطوارئ
            emergency_status = self._check_emergency_conditions()
            
            # حساب الإحصائيات
            stats = self._calculate_session_stats()
            
            logger.info(f"📊 Trade recorded: {result} - Amount: {trade_amount}, Profit: {profit}")
            
            return {
                'trade_recorded': True,
                'session_stats': stats,
                'emergency_status': emergency_status,
                'continue_trading': emergency_status['continue_allowed']
            }
            
        except Exception as e:
            logger.error(f"Error recording trade result: {e}")
            return {'trade_recorded': False, 'error': str(e)}
    
    def _calculate_session_loss_percent(self) -> float:
        """حساب نسبة خسارة الجلسة"""
        if self.current_session['initial_balance'] == 0:
            return 0
        
        loss = self.current_session['initial_balance'] - self.current_session['current_balance']
        return (loss / self.current_session['initial_balance']) * 100
    
    def _count_trades_last_hour(self) -> int:
        """عد الصفقات في الساعة الأخيرة"""
        # هذا مبسط - في التطبيق الحقيقي نحتاج تتبع أوقات الصفقات
        return self.current_session['trades_count']
    
    def _check_emergency_conditions(self) -> Dict[str, Any]:
        """فحص حالات الطوارئ"""
        emergency = {
            'emergency_stop': False,
            'continue_allowed': True,
            'reasons': []
        }
        
        # فحص الإيقاف الطارئ
        session_loss_percent = self._calculate_session_loss_percent()
        if session_loss_percent >= self.config['emergency_stop_loss_percent']:
            emergency['emergency_stop'] = True
            emergency['continue_allowed'] = False
            emergency['reasons'].append(f"Emergency stop: {session_loss_percent:.1f}% loss")
        
        # فحص الخسائر المتتالية
        if self.current_session['consecutive_losses'] >= self.config['max_consecutive_losses']:
            emergency['continue_allowed'] = False
            emergency['reasons'].append(f"Max consecutive losses: {self.current_session['consecutive_losses']}")
        
        return emergency
    
    def _calculate_session_stats(self) -> Dict[str, Any]:
        """حساب إحصائيات الجلسة"""
        total_trades = self.current_session['trades_count']
        win_rate = (self.current_session['wins'] / total_trades * 100) if total_trades > 0 else 0
        
        return {
            'total_trades': total_trades,
            'wins': self.current_session['wins'],
            'losses': self.current_session['losses'],
            'win_rate': win_rate,
            'total_profit': self.current_session['total_profit'],
            'current_balance': self.current_session['current_balance'],
            'session_loss_percent': self._calculate_session_loss_percent(),
            'consecutive_losses': self.current_session['consecutive_losses']
        }
    
    def _get_current_risk_limits(self) -> Dict[str, Any]:
        """الحصول على حدود المخاطرة الحالية"""
        return {
            'max_risk_per_trade_percent': self.config['max_risk_per_trade_percent'],
            'max_session_loss_percent': self.config['max_session_loss_percent'],
            'max_consecutive_losses': self.config['max_consecutive_losses'],
            'min_confidence_required': self.config['min_confidence_required'],
            'max_trades_per_session': self.config['max_trades_per_session']
        }
    
    def get_recommended_trade_amount(self, current_balance: float) -> float:
        """الحصول على مبلغ الصفقة المقترح"""
        recommended = (current_balance * self.config['max_risk_per_trade_percent']) / 100
        return min(max(recommended, self.config['min_trade_amount']), self.config['max_trade_amount'])
    
    def save_session(self, filename: str = None) -> str:
        """حفظ بيانات الجلسة"""
        try:
            if not filename:
                filename = f"session_{self.current_session.get('session_id', 'unknown')}.json"
            
            session_data = self.current_session.copy()
            session_data['end_time'] = datetime.now().isoformat()
            session_data['start_time'] = session_data['start_time'].isoformat() if session_data['start_time'] else None
            session_data['last_trade_time'] = session_data['last_trade_time'].isoformat() if session_data['last_trade_time'] else None
            
            os.makedirs("data/sessions", exist_ok=True)
            filepath = os.path.join("data/sessions", filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Session saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving session: {e}")
            return ""
