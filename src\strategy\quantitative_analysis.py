"""
Quantitative Analysis Layer - الطبقة الثانية
تحليل كمي متقدم باستخدام النماذج الرياضية والإحصائية
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
import json
import os
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score

logger = logging.getLogger(__name__)

class QuantitativeAnalysisLayer:
    """الطبقة الثانية: التحليل الكمي"""
    
    def __init__(self):
        """تهيئة طبقة التحليل الكمي"""
        self.name = "Quantitative Analysis Layer"
        self.weight = 0.25  # وزن هذه الطبقة في القرار النهائي
        
        # إعدادات التحليل الكمي
        self.zscore_window = 20
        self.volatility_window = 14
        self.correlation_window = 50
        self.probability_window = 100
        
        # تخزين البيانات التاريخية للتحليل
        self.historical_signals = []
        self.performance_metrics = {}
        
        logger.info(f"✅ {self.name} initialized")
    
    def calculate_zscore(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        حساب Z-Score لتحديد الانحرافات السعرية
        
        Args:
            candles: قائمة الشموع
            
        Returns:
            نتائج Z-Score
        """
        try:
            df = pd.DataFrame(candles)
            df['close'] = pd.to_numeric(df['close'])
            
            if len(df) < self.zscore_window:
                return {"error": "Not enough data for Z-Score calculation"}
            
            # حساب المتوسط والانحراف المعياري
            rolling_mean = df['close'].rolling(window=self.zscore_window).mean()
            rolling_std = df['close'].rolling(window=self.zscore_window).std()
            
            # حساب Z-Score
            zscore = (df['close'] - rolling_mean) / rolling_std
            
            current_zscore = zscore.iloc[-1]
            
            # تحليل Z-Score
            zscore_signal = self._analyze_zscore_signal(current_zscore)
            
            return {
                'current_zscore': current_zscore,
                'zscore_signal': zscore_signal,
                'mean_reversion_probability': self._calculate_mean_reversion_probability(zscore),
                'extreme_level': self._determine_extreme_level(current_zscore)
            }
            
        except Exception as e:
            logger.error(f"Error calculating Z-Score: {e}")
            return {"error": str(e)}
    
    def calculate_volatility_metrics(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """حساب مقاييس التقلبات"""
        try:
            df = pd.DataFrame(candles)
            df['close'] = pd.to_numeric(df['close'])
            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])
            
            if len(df) < self.volatility_window:
                return {"error": "Not enough data for volatility calculation"}
            
            # حساب العوائد
            returns = df['close'].pct_change().dropna()
            
            # حساب التقلبات التاريخية
            historical_volatility = returns.rolling(window=self.volatility_window).std() * np.sqrt(252)
            
            # حساب ATR
            atr = self._calculate_atr(df)
            
            # حساب Parkinson Volatility
            parkinson_vol = self._calculate_parkinson_volatility(df)
            
            current_vol = historical_volatility.iloc[-1]
            current_atr = atr.iloc[-1] if len(atr) > 0 else 0
            
            return {
                'historical_volatility': current_vol,
                'atr': current_atr,
                'parkinson_volatility': parkinson_vol,
                'volatility_regime': self._determine_volatility_regime(historical_volatility),
                'volatility_percentile': self._calculate_volatility_percentile(current_vol, historical_volatility)
            }
            
        except Exception as e:
            logger.error(f"Error calculating volatility metrics: {e}")
            return {"error": str(e)}
    
    def calculate_correlation_matrix(self, pair_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """حساب مصفوفة الارتباط بين الأزواج"""
        try:
            if len(pair_data) < 2:
                return {"error": "Need at least 2 pairs for correlation analysis"}
            
            # تحويل البيانات إلى DataFrame
            price_data = {}
            for pair_name, candles in pair_data.items():
                df = pd.DataFrame(candles)
                if len(df) >= self.correlation_window:
                    df['close'] = pd.to_numeric(df['close'])
                    price_data[pair_name] = df['close'].tail(self.correlation_window)
            
            if len(price_data) < 2:
                return {"error": "Not enough valid pair data for correlation"}
            
            # إنشاء DataFrame للأسعار
            prices_df = pd.DataFrame(price_data)
            
            # حساب العوائد
            returns_df = prices_df.pct_change().dropna()
            
            # حساب مصفوفة الارتباط
            correlation_matrix = returns_df.corr()
            
            return {
                'correlation_matrix': correlation_matrix.to_dict(),
                'average_correlation': correlation_matrix.mean().mean(),
                'correlation_strength': self._analyze_correlation_strength(correlation_matrix)
            }
            
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return {"error": str(e)}
    
    def calculate_probability_filters(self, candles: List[Dict[str, Any]], 
                                    historical_signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """حساب مرشحات الاحتمالية"""
        try:
            if len(historical_signals) < 10:
                return {"error": "Not enough historical signals for probability analysis"}
            
            # تحليل معدل النجاح التاريخي
            win_rate = self._calculate_historical_win_rate(historical_signals)
            
            # حساب احتمالية النجاح حسب الظروف الحالية
            current_conditions = self._extract_current_conditions(candles)
            conditional_probability = self._calculate_conditional_probability(
                historical_signals, current_conditions
            )
            
            # حساب Sharpe Ratio
            sharpe_ratio = self._calculate_sharpe_ratio(historical_signals)
            
            # حساب Maximum Drawdown
            max_drawdown = self._calculate_max_drawdown(historical_signals)
            
            return {
                'overall_win_rate': win_rate,
                'conditional_probability': conditional_probability,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'confidence_level': self._calculate_confidence_level(win_rate, len(historical_signals))
            }
            
        except Exception as e:
            logger.error(f"Error calculating probability filters: {e}")
            return {"error": str(e)}
    
    def calculate_statistical_tests(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """إجراء الاختبارات الإحصائية"""
        try:
            df = pd.DataFrame(candles)
            df['close'] = pd.to_numeric(df['close'])
            
            if len(df) < 30:
                return {"error": "Not enough data for statistical tests"}
            
            # حساب العوائد
            returns = df['close'].pct_change().dropna()
            
            # اختبار الطبيعية (Normality Test)
            normality_test = self._test_normality(returns)
            
            # اختبار الاستقرارية (Stationarity Test)
            stationarity_test = self._test_stationarity(df['close'])
            
            # اختبار الارتباط التسلسلي (Autocorrelation Test)
            autocorr_test = self._test_autocorrelation(returns)
            
            # اختبار التجانس (Heteroscedasticity Test)
            heteroscedasticity_test = self._test_heteroscedasticity(returns)
            
            return {
                'normality_test': normality_test,
                'stationarity_test': stationarity_test,
                'autocorrelation_test': autocorr_test,
                'heteroscedasticity_test': heteroscedasticity_test,
                'statistical_summary': self._generate_statistical_summary(returns)
            }
            
        except Exception as e:
            logger.error(f"Error in statistical tests: {e}")
            return {"error": str(e)}
    
    def generate_quantitative_signal(self, candles: List[Dict[str, Any]], 
                                   pair_data: Optional[Dict[str, List[Dict[str, Any]]]] = None,
                                   historical_signals: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """توليد الإشارة الكمية النهائية"""
        try:
            # تحليل جميع المكونات الكمية
            zscore_analysis = self.calculate_zscore(candles)
            volatility_analysis = self.calculate_volatility_metrics(candles)
            statistical_analysis = self.calculate_statistical_tests(candles)
            
            # تحليل الارتباط إذا توفرت بيانات أزواج أخرى
            correlation_analysis = {}
            if pair_data:
                correlation_analysis = self.calculate_correlation_matrix(pair_data)
            
            # تحليل الاحتمالية إذا توفرت إشارات تاريخية
            probability_analysis = {}
            if historical_signals:
                probability_analysis = self.calculate_probability_filters(candles, historical_signals)
            
            # حساب النقاط لكل نوع تحليل
            scores = {
                'zscore_score': self._calculate_zscore_score(zscore_analysis),
                'volatility_score': self._calculate_volatility_score(volatility_analysis),
                'statistical_score': self._calculate_statistical_score(statistical_analysis),
                'correlation_score': self._calculate_correlation_score(correlation_analysis),
                'probability_score': self._calculate_probability_score(probability_analysis)
            }
            
            # حساب النتيجة الإجمالية
            valid_scores = [score for score in scores.values() if score != 0]
            total_score = sum(valid_scores) / len(valid_scores) if valid_scores else 0
            
            # تحديد الاتجاه والقوة
            direction = "CALL" if total_score > 0 else "PUT"
            strength = abs(total_score)
            confidence = min(strength * 100, 100)
            
            # تحديد زمن الصفقة المقترح
            suggested_expiry = self._suggest_expiry_time(strength, confidence, volatility_analysis)

            return {
                'layer': self.name,
                'weight': self.weight,
                'signal': direction,
                'strength': strength,
                'confidence': confidence,
                'suggested_expiry_minutes': suggested_expiry,
                'scores': scores,
                'total_score': total_score,
                'analysis_details': {
                    'zscore': zscore_analysis,
                    'volatility': volatility_analysis,
                    'statistical': statistical_analysis,
                    'correlation': correlation_analysis,
                    'probability': probability_analysis
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating quantitative signal: {e}")
            return {
                'layer': self.name,
                'error': str(e),
                'signal': 'NEUTRAL',
                'strength': 0,
                'confidence': 0
            }

    # Helper methods
    def _analyze_zscore_signal(self, zscore: float) -> Dict[str, Any]:
        """تحليل إشارة Z-Score"""
        if abs(zscore) > 2.5:
            signal = "EXTREME_REVERSAL"
            strength = 0.9
        elif abs(zscore) > 2.0:
            signal = "STRONG_REVERSAL"
            strength = 0.7
        elif abs(zscore) > 1.5:
            signal = "MODERATE_REVERSAL"
            strength = 0.5
        else:
            signal = "NORMAL"
            strength = 0.2

        direction = "CALL" if zscore < 0 else "PUT"  # Mean reversion logic

        return {
            'signal': signal,
            'direction': direction,
            'strength': strength,
            'zscore_value': zscore
        }

    def _calculate_mean_reversion_probability(self, zscore_series: pd.Series) -> float:
        """حساب احتمالية العودة للمتوسط"""
        if len(zscore_series) < 10:
            return 0.5

        # حساب عدد المرات التي عاد فيها السعر للمتوسط بعد انحراف كبير
        extreme_points = abs(zscore_series) > 2.0
        reversions = 0
        total_extremes = 0

        for i in range(len(zscore_series) - 5):
            if extreme_points.iloc[i]:
                total_extremes += 1
                # فحص العودة خلال الـ 5 فترات التالية
                future_zscores = zscore_series.iloc[i+1:i+6]
                if any(abs(future_zscores) < 0.5):
                    reversions += 1

        return reversions / max(total_extremes, 1)

    def _determine_extreme_level(self, zscore: float) -> str:
        """تحديد مستوى التطرف"""
        abs_zscore = abs(zscore)
        if abs_zscore > 3.0:
            return "VERY_EXTREME"
        elif abs_zscore > 2.5:
            return "EXTREME"
        elif abs_zscore > 2.0:
            return "HIGH"
        elif abs_zscore > 1.0:
            return "MODERATE"
        else:
            return "NORMAL"

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """حساب Average True Range"""
        high = df['high']
        low = df['low']
        close = df['close']

        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()

        return atr

    def _calculate_parkinson_volatility(self, df: pd.DataFrame) -> float:
        """حساب Parkinson Volatility"""
        if len(df) < 2:
            return 0

        high = df['high']
        low = df['low']

        # Parkinson volatility formula
        log_hl = np.log(high / low)
        parkinson_vol = np.sqrt(np.mean(log_hl ** 2) / (4 * np.log(2)))

        return parkinson_vol

    def _determine_volatility_regime(self, volatility_series: pd.Series) -> str:
        """تحديد نظام التقلبات"""
        if len(volatility_series) < 20:
            return "UNKNOWN"

        current_vol = volatility_series.iloc[-1]
        vol_percentile = stats.percentileofscore(volatility_series.dropna(), current_vol)

        if vol_percentile > 80:
            return "HIGH_VOLATILITY"
        elif vol_percentile > 60:
            return "ELEVATED_VOLATILITY"
        elif vol_percentile > 40:
            return "NORMAL_VOLATILITY"
        elif vol_percentile > 20:
            return "LOW_VOLATILITY"
        else:
            return "VERY_LOW_VOLATILITY"

    def _calculate_volatility_percentile(self, current_vol: float, vol_series: pd.Series) -> float:
        """حساب المئوية للتقلبات الحالية"""
        if len(vol_series) < 10:
            return 50.0

        return stats.percentileofscore(vol_series.dropna(), current_vol)

    def _analyze_correlation_strength(self, correlation_matrix: pd.DataFrame) -> Dict[str, Any]:
        """تحليل قوة الارتباط"""
        if correlation_matrix.empty:
            return {"strength": "UNKNOWN", "average": 0}

        # استخراج القيم العلوية للمصفوفة (بدون القطر الرئيسي)
        upper_triangle = correlation_matrix.where(
            np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
        )
        correlations = upper_triangle.stack().abs()

        if len(correlations) == 0:
            return {"strength": "UNKNOWN", "average": 0}

        avg_correlation = correlations.mean()

        if avg_correlation > 0.8:
            strength = "VERY_HIGH"
        elif avg_correlation > 0.6:
            strength = "HIGH"
        elif avg_correlation > 0.4:
            strength = "MODERATE"
        elif avg_correlation > 0.2:
            strength = "LOW"
        else:
            strength = "VERY_LOW"

        return {
            "strength": strength,
            "average": avg_correlation,
            "max": correlations.max(),
            "min": correlations.min()
        }

    def _calculate_historical_win_rate(self, historical_signals: List[Dict[str, Any]]) -> float:
        """حساب معدل النجاح التاريخي"""
        if not historical_signals:
            return 0.5

        wins = sum(1 for signal in historical_signals if signal.get('result') == 'WIN')
        total = len(historical_signals)

        return wins / total if total > 0 else 0.5

    def _extract_current_conditions(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """استخراج الظروف الحالية للسوق"""
        if len(candles) < 10:
            return {}

        df = pd.DataFrame(candles)
        df['close'] = pd.to_numeric(df['close'])

        # حساب التقلبات الحالية
        returns = df['close'].pct_change().dropna()
        current_volatility = returns.tail(10).std()

        # حساب الترند
        recent_trend = (df['close'].iloc[-1] - df['close'].iloc[-10]) / df['close'].iloc[-10]

        # تحديد وقت اليوم (إذا كان متاحاً)
        current_hour = datetime.now().hour

        return {
            'volatility_level': 'HIGH' if current_volatility > 0.02 else 'LOW',
            'trend_direction': 'UP' if recent_trend > 0.001 else 'DOWN',
            'time_of_day': self._categorize_time_of_day(current_hour)
        }

    def _categorize_time_of_day(self, hour: int) -> str:
        """تصنيف وقت اليوم"""
        if 8 <= hour < 12:
            return 'MORNING'
        elif 12 <= hour < 17:
            return 'AFTERNOON'
        elif 17 <= hour < 22:
            return 'EVENING'
        else:
            return 'NIGHT'

    def _calculate_conditional_probability(self, historical_signals: List[Dict[str, Any]],
                                         current_conditions: Dict[str, Any]) -> float:
        """حساب الاحتمالية المشروطة"""
        if not historical_signals or not current_conditions:
            return 0.5

        # فلترة الإشارات التي تطابق الظروف الحالية
        matching_signals = []
        for signal in historical_signals:
            signal_conditions = signal.get('conditions', {})

            # فحص تطابق الظروف
            matches = 0
            total_conditions = 0

            for condition, value in current_conditions.items():
                if condition in signal_conditions:
                    total_conditions += 1
                    if signal_conditions[condition] == value:
                        matches += 1

            # إذا تطابقت 70% من الظروف على الأقل
            if total_conditions > 0 and matches / total_conditions >= 0.7:
                matching_signals.append(signal)

        if not matching_signals:
            return 0.5

        # حساب معدل النجاح للإشارات المطابقة
        wins = sum(1 for signal in matching_signals if signal.get('result') == 'WIN')
        return wins / len(matching_signals)

    def _calculate_sharpe_ratio(self, historical_signals: List[Dict[str, Any]]) -> float:
        """حساب نسبة شارب"""
        if len(historical_signals) < 10:
            return 0

        # استخراج العوائد
        returns = []
        for signal in historical_signals:
            if 'return' in signal:
                returns.append(signal['return'])
            elif signal.get('result') == 'WIN':
                returns.append(0.8)  # افتراض عائد 80% للفوز
            elif signal.get('result') == 'LOSS':
                returns.append(-1.0)  # افتراض خسارة 100%

        if not returns:
            return 0

        returns_array = np.array(returns)
        mean_return = np.mean(returns_array)
        std_return = np.std(returns_array)

        return mean_return / std_return if std_return != 0 else 0

    def _calculate_max_drawdown(self, historical_signals: List[Dict[str, Any]]) -> float:
        """حساب أقصى انخفاض"""
        if not historical_signals:
            return 0

        # حساب الرصيد التراكمي
        cumulative_balance = [1000]  # رصيد ابتدائي

        for signal in historical_signals:
            current_balance = cumulative_balance[-1]

            if signal.get('result') == 'WIN':
                new_balance = current_balance * 1.8  # ربح 80%
            elif signal.get('result') == 'LOSS':
                new_balance = current_balance * 0.0  # خسارة كاملة
            else:
                new_balance = current_balance

            cumulative_balance.append(new_balance)

        # حساب أقصى انخفاض
        peak = cumulative_balance[0]
        max_drawdown = 0

        for balance in cumulative_balance[1:]:
            if balance > peak:
                peak = balance
            else:
                drawdown = (peak - balance) / peak
                max_drawdown = max(max_drawdown, drawdown)

        return max_drawdown

    def _calculate_confidence_level(self, win_rate: float, sample_size: int) -> float:
        """حساب مستوى الثقة الإحصائي"""
        if sample_size < 10:
            return 0.5

        # حساب الخطأ المعياري
        standard_error = np.sqrt((win_rate * (1 - win_rate)) / sample_size)

        # حساب فترة الثقة 95%
        confidence_interval = 1.96 * standard_error

        # تحويل إلى مستوى ثقة (0-1)
        confidence = 1 - (confidence_interval * 2)

        return max(0, min(1, confidence))

    def _test_normality(self, returns: pd.Series) -> Dict[str, Any]:
        """اختبار الطبيعية للعوائد"""
        try:
            from scipy.stats import shapiro, jarque_bera

            # Shapiro-Wilk test
            shapiro_stat, shapiro_p = shapiro(returns.dropna())

            # Jarque-Bera test
            jb_stat, jb_p = jarque_bera(returns.dropna())

            return {
                'shapiro_test': {
                    'statistic': shapiro_stat,
                    'p_value': shapiro_p,
                    'is_normal': shapiro_p > 0.05
                },
                'jarque_bera_test': {
                    'statistic': jb_stat,
                    'p_value': jb_p,
                    'is_normal': jb_p > 0.05
                }
            }
        except Exception as e:
            return {'error': str(e)}

    def _test_stationarity(self, price_series: pd.Series) -> Dict[str, Any]:
        """اختبار الاستقرارية"""
        try:
            from statsmodels.tsa.stattools import adfuller

            result = adfuller(price_series.dropna())

            return {
                'adf_statistic': result[0],
                'p_value': result[1],
                'critical_values': result[4],
                'is_stationary': result[1] < 0.05
            }
        except Exception as e:
            return {'error': str(e)}

    def _test_autocorrelation(self, returns: pd.Series) -> Dict[str, Any]:
        """اختبار الارتباط التسلسلي"""
        try:
            from statsmodels.stats.diagnostic import acorr_ljungbox

            result = acorr_ljungbox(returns.dropna(), lags=10, return_df=True)

            return {
                'ljung_box_statistic': result['lb_stat'].iloc[-1],
                'p_value': result['lb_pvalue'].iloc[-1],
                'has_autocorrelation': result['lb_pvalue'].iloc[-1] < 0.05
            }
        except Exception as e:
            return {'error': str(e)}

    def _test_heteroscedasticity(self, returns: pd.Series) -> Dict[str, Any]:
        """اختبار التجانس في التباين"""
        try:
            from statsmodels.stats.diagnostic import het_breuschpagan

            # إنشاء متغير مستقل بسيط (الزمن)
            X = np.arange(len(returns)).reshape(-1, 1)

            # إجراء اختبار Breusch-Pagan
            lm_stat, lm_p, f_stat, f_p = het_breuschpagan(returns.dropna(), X[:len(returns.dropna())])

            return {
                'breusch_pagan_statistic': lm_stat,
                'p_value': lm_p,
                'has_heteroscedasticity': lm_p < 0.05
            }
        except Exception as e:
            return {'error': str(e)}

    def _generate_statistical_summary(self, returns: pd.Series) -> Dict[str, Any]:
        """إنشاء ملخص إحصائي"""
        try:
            return {
                'mean': returns.mean(),
                'std': returns.std(),
                'skewness': returns.skew(),
                'kurtosis': returns.kurtosis(),
                'min': returns.min(),
                'max': returns.max(),
                'count': len(returns)
            }
        except Exception as e:
            return {'error': str(e)}

    # Scoring methods
    def _calculate_zscore_score(self, zscore_analysis: Dict[str, Any]) -> float:
        """حساب نقاط Z-Score"""
        if 'error' in zscore_analysis:
            return 0

        zscore_signal = zscore_analysis.get('zscore_signal', {})
        direction = zscore_signal.get('direction', 'NEUTRAL')
        strength = zscore_signal.get('strength', 0)

        score = strength if direction == 'CALL' else -strength

        # تعديل النقاط حسب احتمالية العودة للمتوسط
        mean_reversion_prob = zscore_analysis.get('mean_reversion_probability', 0.5)
        score *= mean_reversion_prob

        return max(-1, min(1, score))

    def _calculate_volatility_score(self, volatility_analysis: Dict[str, Any]) -> float:
        """حساب نقاط التقلبات"""
        if 'error' in volatility_analysis:
            return 0

        volatility_regime = volatility_analysis.get('volatility_regime', 'NORMAL_VOLATILITY')
        volatility_percentile = volatility_analysis.get('volatility_percentile', 50)

        # تقليل النقاط في حالة التقلبات العالية
        if volatility_regime in ['HIGH_VOLATILITY', 'VERY_HIGH_VOLATILITY']:
            return -0.3
        elif volatility_regime == 'VERY_LOW_VOLATILITY':
            return 0.2
        else:
            return 0

    def _calculate_statistical_score(self, statistical_analysis: Dict[str, Any]) -> float:
        """حساب نقاط التحليل الإحصائي"""
        if 'error' in statistical_analysis:
            return 0

        score = 0

        # نقاط الطبيعية
        normality_test = statistical_analysis.get('normality_test', {})
        if normality_test.get('shapiro_test', {}).get('is_normal', False):
            score += 0.1

        # نقاط الاستقرارية
        stationarity_test = statistical_analysis.get('stationarity_test', {})
        if stationarity_test.get('is_stationary', False):
            score += 0.2

        # نقاط الارتباط التسلسلي
        autocorr_test = statistical_analysis.get('autocorrelation_test', {})
        if not autocorr_test.get('has_autocorrelation', True):
            score += 0.1

        return max(-1, min(1, score))

    def _calculate_correlation_score(self, correlation_analysis: Dict[str, Any]) -> float:
        """حساب نقاط الارتباط"""
        if 'error' in correlation_analysis or not correlation_analysis:
            return 0

        correlation_strength = correlation_analysis.get('correlation_strength', {})
        strength_level = correlation_strength.get('strength', 'UNKNOWN')

        # تقليل النقاط في حالة الارتباط العالي (مخاطر أعلى)
        if strength_level == 'VERY_HIGH':
            return -0.3
        elif strength_level == 'HIGH':
            return -0.2
        elif strength_level == 'MODERATE':
            return 0
        else:
            return 0.1

    def _calculate_probability_score(self, probability_analysis: Dict[str, Any]) -> float:
        """حساب نقاط الاحتمالية"""
        if 'error' in probability_analysis or not probability_analysis:
            return 0

        win_rate = probability_analysis.get('overall_win_rate', 0.5)
        conditional_prob = probability_analysis.get('conditional_probability', 0.5)
        sharpe_ratio = probability_analysis.get('sharpe_ratio', 0)
        confidence_level = probability_analysis.get('confidence_level', 0.5)

        # حساب النقاط المركبة
        score = 0

        # نقاط معدل النجاح
        if win_rate > 0.6:
            score += 0.3
        elif win_rate < 0.4:
            score -= 0.3

        # نقاط الاحتمالية المشروطة
        if conditional_prob > 0.65:
            score += 0.4
        elif conditional_prob < 0.35:
            score -= 0.4

        # نقاط نسبة شارب
        if sharpe_ratio > 1.0:
            score += 0.2
        elif sharpe_ratio < 0:
            score -= 0.2

        # تعديل حسب مستوى الثقة
        score *= confidence_level

        return max(-1, min(1, score))

    def _suggest_expiry_time(self, strength: float, confidence: float, volatility_analysis: Dict[str, Any]) -> int:
        """اقتراح زمن انتهاء الصفقة بناءً على التحليل الكمي"""

        # تحديد نظام التقلبات
        volatility_regime = volatility_analysis.get('volatility_regime', 'NORMAL_VOLATILITY')
        volatility_percentile = volatility_analysis.get('volatility_percentile', 50)

        # إشارة قوية جداً مع تقلبات منخفضة
        if confidence >= 90 and strength >= 0.8:
            if 'HIGH' in volatility_regime:
                return 1  # دقيقة واحدة
            elif 'LOW' in volatility_regime:
                return 3  # ثلاث دقائق للاستفادة من الاستقرار
            else:
                return 2  # دقيقتان

        # إشارة قوية
        elif confidence >= 80 and strength >= 0.6:
            if 'HIGH' in volatility_regime:
                return 2  # دقيقتان
            elif 'LOW' in volatility_regime:
                return 5  # خمس دقائق
            else:
                return 3  # ثلاث دقائق

        # إشارة متوسطة
        elif confidence >= 70 and strength >= 0.4:
            if 'HIGH' in volatility_regime:
                return 3  # ثلاث دقائق
            else:
                return 5  # خمس دقائق

        # إشارة ضعيفة
        else:
            return 5  # خمس دقائق كحد أقصى
