2025-08-02 03:29:02,624 - __main__ - INFO - Starting Quotex Trading System...
2025-08-02 03:29:02,624 - __main__ - INFO - Connecting to Quotex platform...
2025-08-02 03:29:04,292 - websocket - INFO - Websocket connected
2025-08-02 03:29:04,317 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-02 03:29:07,359 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-02 03:29:07,901 - __main__ - INFO - Account: Nardelit
2025-08-02 03:29:07,901 - __main__ - INFO - Balance: 10034.12 $
2025-08-02 03:29:07,901 - __main__ - INFO - Initializing data managers...
2025-08-02 03:29:07,901 - __main__ - INFO - Data managers initialized successfully
2025-08-02 03:29:07,901 - __main__ - INFO - Starting data collection processes...
2025-08-02 03:29:07,901 - src.forex_pairs.pairs_manager - INFO - Starting forex pairs monitoring...
2025-08-02 03:29:07,901 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:29:07,901 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:29:07,915 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:29:07,919 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:29:07,919 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:29:07,919 - src.historical_data.historical_manager - INFO - Starting historical data collection...
2025-08-02 03:29:07,932 - src.historical_data.historical_manager - INFO - Updating historical data for 110 forex pairs
2025-08-02 03:29:07,932 - src.historical_data.historical_manager - INFO - Updating historical data for ADAUSD_otc
2025-08-02 03:29:07,936 - src.historical_data.historical_manager - INFO - No existing data for ADAUSD_otc, fetching 30 days
2025-08-02 03:29:07,936 - src.historical_data.historical_manager - INFO - Fetching historical data for ADAUSD_otc (30 days)
2025-08-02 03:29:08,267 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ADAUSD_otc
2025-08-02 03:29:08,270 - src.historical_data.historical_manager - INFO - Saved 199 candles for ADAUSD_otc
2025-08-02 03:29:08,270 - src.historical_data.historical_manager - INFO - Updated historical data for ADAUSD_otc
2025-08-02 03:29:10,271 - src.historical_data.historical_manager - INFO - Updating historical data for APTUSD_otc
2025-08-02 03:29:10,272 - src.historical_data.historical_manager - INFO - No existing data for APTUSD_otc, fetching 30 days
2025-08-02 03:29:10,272 - src.historical_data.historical_manager - INFO - Fetching historical data for APTUSD_otc (30 days)
2025-08-02 03:29:10,604 - src.historical_data.historical_manager - INFO - Fetched 199 candles for APTUSD_otc
2025-08-02 03:29:10,610 - src.historical_data.historical_manager - INFO - Saved 199 candles for APTUSD_otc
2025-08-02 03:29:10,610 - src.historical_data.historical_manager - INFO - Updated historical data for APTUSD_otc
2025-08-02 03:29:12,625 - src.historical_data.historical_manager - INFO - Updating historical data for ARBUSD_otc
2025-08-02 03:29:12,625 - src.historical_data.historical_manager - INFO - No existing data for ARBUSD_otc, fetching 30 days
2025-08-02 03:29:12,625 - src.historical_data.historical_manager - INFO - Fetching historical data for ARBUSD_otc (30 days)
2025-08-02 03:29:12,943 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ARBUSD_otc
2025-08-02 03:29:12,948 - src.historical_data.historical_manager - INFO - Saved 199 candles for ARBUSD_otc
2025-08-02 03:29:12,948 - src.historical_data.historical_manager - INFO - Updated historical data for ARBUSD_otc
2025-08-02 03:29:14,958 - src.historical_data.historical_manager - INFO - Updating historical data for ATOUSD_otc
2025-08-02 03:29:14,958 - src.historical_data.historical_manager - INFO - No existing data for ATOUSD_otc, fetching 30 days
2025-08-02 03:29:14,958 - src.historical_data.historical_manager - INFO - Fetching historical data for ATOUSD_otc (30 days)
2025-08-02 03:29:15,284 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ATOUSD_otc
2025-08-02 03:29:15,296 - src.historical_data.historical_manager - INFO - Saved 199 candles for ATOUSD_otc
2025-08-02 03:29:15,296 - src.historical_data.historical_manager - INFO - Updated historical data for ATOUSD_otc
2025-08-02 03:29:17,310 - src.historical_data.historical_manager - INFO - Updating historical data for AUDCAD
2025-08-02 03:29:17,310 - src.historical_data.historical_manager - INFO - No existing data for AUDCAD, fetching 30 days
2025-08-02 03:29:17,312 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDCAD (30 days)
2025-08-02 03:29:17,655 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDCAD
2025-08-02 03:29:17,667 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDCAD
2025-08-02 03:29:17,667 - src.historical_data.historical_manager - INFO - Updated historical data for AUDCAD
2025-08-02 03:29:19,681 - src.historical_data.historical_manager - INFO - Updating historical data for AUDCAD_otc
2025-08-02 03:29:19,682 - src.historical_data.historical_manager - INFO - Last data for AUDCAD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:29:19,682 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDCAD_otc (1 days)
2025-08-02 03:29:20,431 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDCAD_otc
2025-08-02 03:29:20,431 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:29:20,433 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDCAD_otc
2025-08-02 03:29:20,433 - src.historical_data.historical_manager - INFO - Updated historical data for AUDCAD_otc
2025-08-02 03:29:22,443 - src.historical_data.historical_manager - INFO - Updating historical data for AUDCHF
2025-08-02 03:29:22,443 - src.historical_data.historical_manager - INFO - No existing data for AUDCHF, fetching 30 days
2025-08-02 03:29:22,443 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDCHF (30 days)
2025-08-02 03:29:22,988 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDCHF
2025-08-02 03:29:22,990 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDCHF
2025-08-02 03:29:22,990 - src.historical_data.historical_manager - INFO - Updated historical data for AUDCHF
2025-08-02 03:29:25,006 - src.historical_data.historical_manager - INFO - Updating historical data for AUDCHF_otc
2025-08-02 03:29:25,008 - src.historical_data.historical_manager - INFO - Last data for AUDCHF_otc is 0.0 days old, fetching 1 days
2025-08-02 03:29:25,011 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDCHF_otc (1 days)
2025-08-02 03:29:25,562 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDCHF_otc
2025-08-02 03:29:25,562 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:29:25,562 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDCHF_otc
2025-08-02 03:29:25,562 - src.historical_data.historical_manager - INFO - Updated historical data for AUDCHF_otc
2025-08-02 03:29:27,574 - src.historical_data.historical_manager - INFO - Updating historical data for AUDJPY
2025-08-02 03:29:27,574 - src.historical_data.historical_manager - INFO - No existing data for AUDJPY, fetching 30 days
2025-08-02 03:29:27,574 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDJPY (30 days)
2025-08-02 03:29:27,906 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDJPY
2025-08-02 03:29:27,908 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDJPY
2025-08-02 03:29:27,908 - src.historical_data.historical_manager - INFO - Updated historical data for AUDJPY
2025-08-02 03:29:29,910 - src.historical_data.historical_manager - INFO - Updating historical data for AUDJPY_otc
2025-08-02 03:29:29,911 - src.historical_data.historical_manager - INFO - Last data for AUDJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:29:29,911 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDJPY_otc (1 days)
2025-08-02 03:29:30,252 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDJPY_otc
2025-08-02 03:29:30,254 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:29:30,259 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDJPY_otc
2025-08-02 03:29:30,259 - src.historical_data.historical_manager - INFO - Updated historical data for AUDJPY_otc
2025-08-02 03:29:32,263 - src.historical_data.historical_manager - INFO - Updating historical data for AUDNZD_otc
2025-08-02 03:29:32,263 - src.historical_data.historical_manager - INFO - No existing data for AUDNZD_otc, fetching 30 days
2025-08-02 03:29:32,265 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDNZD_otc (30 days)
2025-08-02 03:29:33,579 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDNZD_otc
2025-08-02 03:29:33,588 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDNZD_otc
2025-08-02 03:29:33,588 - src.historical_data.historical_manager - INFO - Updated historical data for AUDNZD_otc
2025-08-02 03:29:35,590 - src.historical_data.historical_manager - INFO - Updating historical data for AUDUSD
2025-08-02 03:29:35,590 - src.historical_data.historical_manager - INFO - No existing data for AUDUSD, fetching 30 days
2025-08-02 03:29:35,590 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDUSD (30 days)
2025-08-02 03:29:35,917 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDUSD
2025-08-02 03:29:35,925 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDUSD
2025-08-02 03:29:35,925 - src.historical_data.historical_manager - INFO - Updated historical data for AUDUSD
2025-08-02 03:29:37,929 - src.historical_data.historical_manager - INFO - Updating historical data for AUDUSD_otc
2025-08-02 03:29:37,930 - src.historical_data.historical_manager - INFO - Last data for AUDUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:29:37,930 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDUSD_otc (1 days)
2025-08-02 03:29:38,253 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDUSD_otc
2025-08-02 03:29:38,253 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:29:38,257 - src.historical_data.historical_manager - INFO - Saved 199 candles for AUDUSD_otc
2025-08-02 03:29:38,257 - src.historical_data.historical_manager - INFO - Updated historical data for AUDUSD_otc
2025-08-02 03:29:40,270 - src.historical_data.historical_manager - INFO - Updating historical data for AVAUSD_otc
2025-08-02 03:29:40,270 - src.historical_data.historical_manager - INFO - No existing data for AVAUSD_otc, fetching 30 days
2025-08-02 03:29:40,270 - src.historical_data.historical_manager - INFO - Fetching historical data for AVAUSD_otc (30 days)
2025-08-02 03:29:40,345 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:29:40,817 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AVAUSD_otc
2025-08-02 03:29:40,823 - src.historical_data.historical_manager - INFO - Saved 199 candles for AVAUSD_otc
2025-08-02 03:29:40,823 - src.historical_data.historical_manager - INFO - Updated historical data for AVAUSD_otc
2025-08-02 03:29:42,827 - src.historical_data.historical_manager - INFO - Updating historical data for AXJAUD
2025-08-02 03:29:42,827 - src.historical_data.historical_manager - INFO - No existing data for AXJAUD, fetching 30 days
2025-08-02 03:29:42,827 - src.historical_data.historical_manager - INFO - Fetching historical data for AXJAUD (30 days)
2025-08-02 03:29:43,078 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AXJAUD
2025-08-02 03:29:43,085 - src.historical_data.historical_manager - INFO - Saved 199 candles for AXJAUD
2025-08-02 03:29:43,093 - src.historical_data.historical_manager - INFO - Updated historical data for AXJAUD
2025-08-02 03:29:45,101 - src.historical_data.historical_manager - INFO - Updating historical data for AXSUSD_otc
2025-08-02 03:29:45,101 - src.historical_data.historical_manager - INFO - No existing data for AXSUSD_otc, fetching 30 days
2025-08-02 03:29:45,101 - src.historical_data.historical_manager - INFO - Fetching historical data for AXSUSD_otc (30 days)
2025-08-02 03:29:45,426 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AXSUSD_otc
2025-08-02 03:29:45,434 - src.historical_data.historical_manager - INFO - Saved 199 candles for AXSUSD_otc
2025-08-02 03:29:45,436 - src.historical_data.historical_manager - INFO - Updated historical data for AXSUSD_otc
2025-08-02 03:29:47,447 - src.historical_data.historical_manager - INFO - Updating historical data for BCHUSD_otc
2025-08-02 03:29:47,447 - src.historical_data.historical_manager - INFO - No existing data for BCHUSD_otc, fetching 30 days
2025-08-02 03:29:47,449 - src.historical_data.historical_manager - INFO - Fetching historical data for BCHUSD_otc (30 days)
2025-08-02 03:29:47,796 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BCHUSD_otc
2025-08-02 03:29:47,804 - src.historical_data.historical_manager - INFO - Saved 199 candles for BCHUSD_otc
2025-08-02 03:29:47,804 - src.historical_data.historical_manager - INFO - Updated historical data for BCHUSD_otc
2025-08-02 03:29:49,666 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:29:49,822 - src.historical_data.historical_manager - INFO - Updating historical data for BEAUSD_otc
2025-08-02 03:29:49,822 - src.historical_data.historical_manager - INFO - No existing data for BEAUSD_otc, fetching 30 days
2025-08-02 03:29:49,822 - src.historical_data.historical_manager - INFO - Fetching historical data for BEAUSD_otc (30 days)
2025-08-02 03:29:50,203 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BEAUSD_otc
2025-08-02 03:29:50,204 - src.historical_data.historical_manager - INFO - Saved 199 candles for BEAUSD_otc
2025-08-02 03:29:50,204 - src.historical_data.historical_manager - INFO - Updated historical data for BEAUSD_otc
2025-08-02 03:29:52,219 - src.historical_data.historical_manager - INFO - Updating historical data for BNBUSD_otc
2025-08-02 03:29:52,219 - src.historical_data.historical_manager - INFO - No existing data for BNBUSD_otc, fetching 30 days
2025-08-02 03:29:52,219 - src.historical_data.historical_manager - INFO - Fetching historical data for BNBUSD_otc (30 days)
2025-08-02 03:29:52,340 - websocket - DEBUG - Sending ping
2025-08-02 03:29:52,562 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BNBUSD_otc
2025-08-02 03:29:52,577 - src.historical_data.historical_manager - INFO - Saved 199 candles for BNBUSD_otc
2025-08-02 03:29:52,577 - src.historical_data.historical_manager - INFO - Updated historical data for BNBUSD_otc
2025-08-02 03:29:54,586 - src.historical_data.historical_manager - INFO - Updating historical data for BONUSD_otc
2025-08-02 03:29:54,586 - src.historical_data.historical_manager - INFO - No existing data for BONUSD_otc, fetching 30 days
2025-08-02 03:29:54,586 - src.historical_data.historical_manager - INFO - Fetching historical data for BONUSD_otc (30 days)
2025-08-02 03:29:54,916 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BONUSD_otc
2025-08-02 03:29:54,931 - src.historical_data.historical_manager - INFO - Saved 199 candles for BONUSD_otc
2025-08-02 03:29:54,934 - src.historical_data.historical_manager - INFO - Updated historical data for BONUSD_otc
2025-08-02 03:29:56,938 - src.historical_data.historical_manager - INFO - Updating historical data for BRLUSD_otc
2025-08-02 03:29:56,938 - src.historical_data.historical_manager - INFO - No existing data for BRLUSD_otc, fetching 30 days
2025-08-02 03:29:56,938 - src.historical_data.historical_manager - INFO - Fetching historical data for BRLUSD_otc (30 days)
2025-08-02 03:29:57,276 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BRLUSD_otc
2025-08-02 03:29:57,281 - src.historical_data.historical_manager - INFO - Saved 199 candles for BRLUSD_otc
2025-08-02 03:29:57,281 - src.historical_data.historical_manager - INFO - Updated historical data for BRLUSD_otc
2025-08-02 03:29:59,293 - src.historical_data.historical_manager - INFO - Updating historical data for BTCUSD_otc
2025-08-02 03:29:59,293 - src.historical_data.historical_manager - INFO - No existing data for BTCUSD_otc, fetching 30 days
2025-08-02 03:29:59,293 - src.historical_data.historical_manager - INFO - Fetching historical data for BTCUSD_otc (30 days)
2025-08-02 03:29:59,627 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BTCUSD_otc
2025-08-02 03:29:59,627 - src.historical_data.historical_manager - INFO - Saved 199 candles for BTCUSD_otc
2025-08-02 03:29:59,627 - src.historical_data.historical_manager - INFO - Updated historical data for BTCUSD_otc
2025-08-02 03:30:01,642 - src.historical_data.historical_manager - INFO - Updating historical data for CADCHF_otc
2025-08-02 03:30:01,642 - src.historical_data.historical_manager - INFO - No existing data for CADCHF_otc, fetching 30 days
2025-08-02 03:30:01,642 - src.historical_data.historical_manager - INFO - Fetching historical data for CADCHF_otc (30 days)
2025-08-02 03:30:02,422 - src.historical_data.historical_manager - INFO - Fetched 200 candles for CADCHF_otc
2025-08-02 03:30:02,423 - src.historical_data.historical_manager - INFO - Saved 200 candles for CADCHF_otc
2025-08-02 03:30:02,423 - src.historical_data.historical_manager - INFO - Updated historical data for CADCHF_otc
2025-08-02 03:30:04,429 - src.historical_data.historical_manager - INFO - Updating historical data for CADJPY
2025-08-02 03:30:04,429 - src.historical_data.historical_manager - INFO - No existing data for CADJPY, fetching 30 days
2025-08-02 03:30:04,429 - src.historical_data.historical_manager - INFO - Fetching historical data for CADJPY (30 days)
2025-08-02 03:30:04,762 - src.historical_data.historical_manager - INFO - Fetched 199 candles for CADJPY
2025-08-02 03:30:04,766 - src.historical_data.historical_manager - INFO - Saved 199 candles for CADJPY
2025-08-02 03:30:04,766 - src.historical_data.historical_manager - INFO - Updated historical data for CADJPY
2025-08-02 03:30:06,784 - src.historical_data.historical_manager - INFO - Updating historical data for CADJPY_otc
2025-08-02 03:30:06,787 - src.historical_data.historical_manager - INFO - Last data for CADJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:30:06,787 - src.historical_data.historical_manager - INFO - Fetching historical data for CADJPY_otc (1 days)
2025-08-02 03:30:07,237 - src.historical_data.historical_manager - INFO - Fetched 199 candles for CADJPY_otc
2025-08-02 03:30:07,238 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:30:07,242 - src.historical_data.historical_manager - INFO - Saved 199 candles for CADJPY_otc
2025-08-02 03:30:07,242 - src.historical_data.historical_manager - INFO - Updated historical data for CADJPY_otc
2025-08-02 03:30:07,926 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:30:07,926 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:30:07,928 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:30:07,928 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:30:07,928 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:30:09,271 - src.historical_data.historical_manager - INFO - Updating historical data for CHFJPY
2025-08-02 03:30:09,271 - src.historical_data.historical_manager - INFO - No existing data for CHFJPY, fetching 30 days
2025-08-02 03:30:09,271 - src.historical_data.historical_manager - INFO - Fetching historical data for CHFJPY (30 days)
2025-08-02 03:30:09,829 - src.historical_data.historical_manager - INFO - Fetched 199 candles for CHFJPY
2025-08-02 03:30:09,832 - src.historical_data.historical_manager - INFO - Saved 199 candles for CHFJPY
2025-08-02 03:30:09,832 - src.historical_data.historical_manager - INFO - Updated historical data for CHFJPY
2025-08-02 03:30:11,839 - src.historical_data.historical_manager - INFO - Updating historical data for CHFJPY_otc
2025-08-02 03:30:11,840 - src.historical_data.historical_manager - INFO - Last data for CHFJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:30:11,840 - src.historical_data.historical_manager - INFO - Fetching historical data for CHFJPY_otc (1 days)
2025-08-02 03:30:12,180 - src.historical_data.historical_manager - INFO - Fetched 199 candles for CHFJPY_otc
2025-08-02 03:30:12,180 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:30:12,185 - src.historical_data.historical_manager - INFO - Saved 199 candles for CHFJPY_otc
2025-08-02 03:30:12,185 - src.historical_data.historical_manager - INFO - Updated historical data for CHFJPY_otc
2025-08-02 03:30:14,200 - src.historical_data.historical_manager - INFO - Updating historical data for DASUSD_otc
2025-08-02 03:30:14,200 - src.historical_data.historical_manager - INFO - No existing data for DASUSD_otc, fetching 30 days
2025-08-02 03:30:14,200 - src.historical_data.historical_manager - INFO - Fetching historical data for DASUSD_otc (30 days)
2025-08-02 03:30:14,518 - src.historical_data.historical_manager - INFO - Fetched 199 candles for DASUSD_otc
2025-08-02 03:30:14,527 - src.historical_data.historical_manager - INFO - Saved 199 candles for DASUSD_otc
2025-08-02 03:30:14,527 - src.historical_data.historical_manager - INFO - Updated historical data for DASUSD_otc
2025-08-02 03:30:16,345 - websocket - DEBUG - Sending ping
2025-08-02 03:30:16,533 - src.historical_data.historical_manager - INFO - Updating historical data for DJIUSD
2025-08-02 03:30:16,533 - src.historical_data.historical_manager - INFO - No existing data for DJIUSD, fetching 30 days
2025-08-02 03:30:16,533 - src.historical_data.historical_manager - INFO - Fetching historical data for DJIUSD (30 days)
2025-08-02 03:30:16,862 - src.historical_data.historical_manager - INFO - Fetched 199 candles for DJIUSD
2025-08-02 03:30:16,870 - src.historical_data.historical_manager - INFO - Saved 199 candles for DJIUSD
2025-08-02 03:30:16,870 - src.historical_data.historical_manager - INFO - Updated historical data for DJIUSD
2025-08-02 03:30:18,880 - src.historical_data.historical_manager - INFO - Updating historical data for DOGUSD_otc
2025-08-02 03:30:18,880 - src.historical_data.historical_manager - INFO - No existing data for DOGUSD_otc, fetching 30 days
2025-08-02 03:30:18,880 - src.historical_data.historical_manager - INFO - Fetching historical data for DOGUSD_otc (30 days)
2025-08-02 03:30:19,207 - src.historical_data.historical_manager - INFO - Fetched 199 candles for DOGUSD_otc
2025-08-02 03:30:19,218 - src.historical_data.historical_manager - INFO - Saved 199 candles for DOGUSD_otc
2025-08-02 03:30:19,218 - src.historical_data.historical_manager - INFO - Updated historical data for DOGUSD_otc
2025-08-02 03:30:21,230 - src.historical_data.historical_manager - INFO - Updating historical data for DOTUSD_otc
2025-08-02 03:30:21,230 - src.historical_data.historical_manager - INFO - No existing data for DOTUSD_otc, fetching 30 days
2025-08-02 03:30:21,233 - src.historical_data.historical_manager - INFO - Fetching historical data for DOTUSD_otc (30 days)
2025-08-02 03:30:21,553 - src.historical_data.historical_manager - INFO - Fetched 199 candles for DOTUSD_otc
2025-08-02 03:30:21,558 - src.historical_data.historical_manager - INFO - Saved 199 candles for DOTUSD_otc
2025-08-02 03:30:21,559 - src.historical_data.historical_manager - INFO - Updated historical data for DOTUSD_otc
2025-08-02 03:30:22,634 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:22,868 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:23,048 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:23,221 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:23,372 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:23,567 - src.historical_data.historical_manager - INFO - Updating historical data for ETCUSD_otc
2025-08-02 03:30:23,568 - src.historical_data.historical_manager - INFO - No existing data for ETCUSD_otc, fetching 30 days
2025-08-02 03:30:23,569 - src.historical_data.historical_manager - INFO - Fetching historical data for ETCUSD_otc (30 days)
2025-08-02 03:30:23,882 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:23,885 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ETCUSD_otc
2025-08-02 03:30:23,893 - src.historical_data.historical_manager - INFO - Saved 199 candles for ETCUSD_otc
2025-08-02 03:30:23,895 - src.historical_data.historical_manager - INFO - Updated historical data for ETCUSD_otc
2025-08-02 03:30:23,910 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:23,940 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:23,974 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:24,002 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:24,027 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:24,063 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:24,092 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:24,124 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:24,153 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:30:25,897 - src.historical_data.historical_manager - INFO - Updating historical data for ETHUSD_otc
2025-08-02 03:30:25,897 - src.historical_data.historical_manager - INFO - No existing data for ETHUSD_otc, fetching 30 days
2025-08-02 03:30:25,897 - src.historical_data.historical_manager - INFO - Fetching historical data for ETHUSD_otc (30 days)
2025-08-02 03:30:26,525 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ETHUSD_otc
2025-08-02 03:30:26,528 - src.historical_data.historical_manager - INFO - Saved 199 candles for ETHUSD_otc
2025-08-02 03:30:26,528 - src.historical_data.historical_manager - INFO - Updated historical data for ETHUSD_otc
2025-08-02 03:30:28,536 - src.historical_data.historical_manager - INFO - Updating historical data for EURAUD
2025-08-02 03:30:28,536 - src.historical_data.historical_manager - INFO - No existing data for EURAUD, fetching 30 days
2025-08-02 03:30:28,536 - src.historical_data.historical_manager - INFO - Fetching historical data for EURAUD (30 days)
2025-08-02 03:30:29,200 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURAUD
2025-08-02 03:30:29,200 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURAUD
2025-08-02 03:30:29,200 - src.historical_data.historical_manager - INFO - Updated historical data for EURAUD
2025-08-02 03:30:31,213 - src.historical_data.historical_manager - INFO - Updating historical data for EURAUD_otc
2025-08-02 03:30:31,217 - src.historical_data.historical_manager - INFO - Last data for EURAUD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:30:31,217 - src.historical_data.historical_manager - INFO - Fetching historical data for EURAUD_otc (1 days)
2025-08-02 03:30:32,324 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURAUD_otc
2025-08-02 03:30:32,324 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:30:32,326 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURAUD_otc
2025-08-02 03:30:32,328 - src.historical_data.historical_manager - INFO - Updated historical data for EURAUD_otc
2025-08-02 03:30:34,331 - src.historical_data.historical_manager - INFO - Updating historical data for EURCAD
2025-08-02 03:30:34,331 - src.historical_data.historical_manager - INFO - No existing data for EURCAD, fetching 30 days
2025-08-02 03:30:34,331 - src.historical_data.historical_manager - INFO - Fetching historical data for EURCAD (30 days)
2025-08-02 03:30:34,664 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURCAD
2025-08-02 03:30:34,667 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURCAD
2025-08-02 03:30:34,667 - src.historical_data.historical_manager - INFO - Updated historical data for EURCAD
2025-08-02 03:30:36,667 - src.historical_data.historical_manager - INFO - Updating historical data for EURCAD_otc
2025-08-02 03:30:36,667 - src.historical_data.historical_manager - INFO - Last data for EURCAD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:30:36,667 - src.historical_data.historical_manager - INFO - Fetching historical data for EURCAD_otc (1 days)
2025-08-02 03:30:36,985 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURCAD_otc
2025-08-02 03:30:36,985 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:30:36,993 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURCAD_otc
2025-08-02 03:30:36,993 - src.historical_data.historical_manager - INFO - Updated historical data for EURCAD_otc
2025-08-02 03:30:38,998 - src.historical_data.historical_manager - INFO - Updating historical data for EURCHF
2025-08-02 03:30:38,998 - src.historical_data.historical_manager - INFO - No existing data for EURCHF, fetching 30 days
2025-08-02 03:30:38,998 - src.historical_data.historical_manager - INFO - Fetching historical data for EURCHF (30 days)
2025-08-02 03:30:39,314 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURCHF
2025-08-02 03:30:39,316 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURCHF
2025-08-02 03:30:39,316 - src.historical_data.historical_manager - INFO - Updated historical data for EURCHF
2025-08-02 03:30:40,355 - websocket - DEBUG - Sending ping
2025-08-02 03:30:41,321 - src.historical_data.historical_manager - INFO - Updating historical data for EURCHF_otc
2025-08-02 03:30:41,322 - src.historical_data.historical_manager - INFO - Last data for EURCHF_otc is 0.0 days old, fetching 1 days
2025-08-02 03:30:41,322 - src.historical_data.historical_manager - INFO - Fetching historical data for EURCHF_otc (1 days)
2025-08-02 03:30:41,651 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURCHF_otc
2025-08-02 03:30:41,651 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:30:41,658 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURCHF_otc
2025-08-02 03:30:41,658 - src.historical_data.historical_manager - INFO - Updated historical data for EURCHF_otc
2025-08-02 03:30:43,660 - src.historical_data.historical_manager - INFO - Updating historical data for EURGBP
2025-08-02 03:30:43,661 - src.historical_data.historical_manager - INFO - No existing data for EURGBP, fetching 30 days
2025-08-02 03:30:43,662 - src.historical_data.historical_manager - INFO - Fetching historical data for EURGBP (30 days)
2025-08-02 03:30:43,994 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURGBP
2025-08-02 03:30:44,008 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURGBP
2025-08-02 03:30:44,008 - src.historical_data.historical_manager - INFO - Updated historical data for EURGBP
2025-08-02 03:30:46,015 - src.historical_data.historical_manager - INFO - Updating historical data for EURGBP_otc
2025-08-02 03:30:46,017 - src.historical_data.historical_manager - INFO - Last data for EURGBP_otc is 0.0 days old, fetching 1 days
2025-08-02 03:30:46,017 - src.historical_data.historical_manager - INFO - Fetching historical data for EURGBP_otc (1 days)
2025-08-02 03:30:46,345 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURGBP_otc
2025-08-02 03:30:46,345 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:30:46,348 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURGBP_otc
2025-08-02 03:30:46,348 - src.historical_data.historical_manager - INFO - Updated historical data for EURGBP_otc
2025-08-02 03:30:48,363 - src.historical_data.historical_manager - INFO - Updating historical data for EURJPY
2025-08-02 03:30:48,363 - src.historical_data.historical_manager - INFO - No existing data for EURJPY, fetching 30 days
2025-08-02 03:30:48,363 - src.historical_data.historical_manager - INFO - Fetching historical data for EURJPY (30 days)
2025-08-02 03:30:48,698 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURJPY
2025-08-02 03:30:48,698 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURJPY
2025-08-02 03:30:48,709 - src.historical_data.historical_manager - INFO - Updated historical data for EURJPY
2025-08-02 03:30:50,724 - src.historical_data.historical_manager - INFO - Updating historical data for EURJPY_otc
2025-08-02 03:30:50,724 - src.historical_data.historical_manager - INFO - Last data for EURJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:30:50,724 - src.historical_data.historical_manager - INFO - Fetching historical data for EURJPY_otc (1 days)
2025-08-02 03:30:51,054 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURJPY_otc
2025-08-02 03:30:51,054 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:30:51,054 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURJPY_otc
2025-08-02 03:30:51,054 - src.historical_data.historical_manager - INFO - Updated historical data for EURJPY_otc
2025-08-02 03:30:53,058 - src.historical_data.historical_manager - INFO - Updating historical data for EURNZD_otc
2025-08-02 03:30:53,058 - src.historical_data.historical_manager - INFO - No existing data for EURNZD_otc, fetching 30 days
2025-08-02 03:30:53,058 - src.historical_data.historical_manager - INFO - Fetching historical data for EURNZD_otc (30 days)
2025-08-02 03:30:53,379 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURNZD_otc
2025-08-02 03:30:53,381 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURNZD_otc
2025-08-02 03:30:53,381 - src.historical_data.historical_manager - INFO - Updated historical data for EURNZD_otc
2025-08-02 03:30:55,388 - src.historical_data.historical_manager - INFO - Updating historical data for EURSGD_otc
2025-08-02 03:30:55,389 - src.historical_data.historical_manager - INFO - No existing data for EURSGD_otc, fetching 30 days
2025-08-02 03:30:55,389 - src.historical_data.historical_manager - INFO - Fetching historical data for EURSGD_otc (30 days)
2025-08-02 03:30:55,725 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURSGD_otc
2025-08-02 03:30:55,726 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURSGD_otc
2025-08-02 03:30:55,726 - src.historical_data.historical_manager - INFO - Updated historical data for EURSGD_otc
2025-08-02 03:30:57,730 - src.historical_data.historical_manager - INFO - Updating historical data for EURUSD
2025-08-02 03:30:57,730 - src.historical_data.historical_manager - INFO - No existing data for EURUSD, fetching 30 days
2025-08-02 03:30:57,732 - src.historical_data.historical_manager - INFO - Fetching historical data for EURUSD (30 days)
2025-08-02 03:30:58,065 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURUSD
2025-08-02 03:30:58,073 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURUSD
2025-08-02 03:30:58,076 - src.historical_data.historical_manager - INFO - Updated historical data for EURUSD
2025-08-02 03:31:00,085 - src.historical_data.historical_manager - INFO - Updating historical data for EURUSD_otc
2025-08-02 03:31:00,085 - src.historical_data.historical_manager - INFO - Last data for EURUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:31:00,085 - src.historical_data.historical_manager - INFO - Fetching historical data for EURUSD_otc (1 days)
2025-08-02 03:31:00,410 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURUSD_otc
2025-08-02 03:31:00,410 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:31:00,412 - src.historical_data.historical_manager - INFO - Saved 199 candles for EURUSD_otc
2025-08-02 03:31:00,412 - src.historical_data.historical_manager - INFO - Updated historical data for EURUSD_otc
2025-08-02 03:31:02,421 - src.historical_data.historical_manager - INFO - Updating historical data for F40EUR
2025-08-02 03:31:02,421 - src.historical_data.historical_manager - INFO - No existing data for F40EUR, fetching 30 days
2025-08-02 03:31:02,421 - src.historical_data.historical_manager - INFO - Fetching historical data for F40EUR (30 days)
2025-08-02 03:31:03,182 - src.historical_data.historical_manager - INFO - Fetched 199 candles for F40EUR
2025-08-02 03:31:03,184 - src.historical_data.historical_manager - INFO - Saved 199 candles for F40EUR
2025-08-02 03:31:03,184 - src.historical_data.historical_manager - INFO - Updated historical data for F40EUR
2025-08-02 03:31:04,364 - websocket - DEBUG - Sending ping
2025-08-02 03:31:05,189 - src.historical_data.historical_manager - INFO - Updating historical data for FLOUSD_otc
2025-08-02 03:31:05,189 - src.historical_data.historical_manager - INFO - No existing data for FLOUSD_otc, fetching 30 days
2025-08-02 03:31:05,189 - src.historical_data.historical_manager - INFO - Fetching historical data for FLOUSD_otc (30 days)
2025-08-02 03:31:05,507 - src.historical_data.historical_manager - INFO - Fetched 199 candles for FLOUSD_otc
2025-08-02 03:31:05,507 - src.historical_data.historical_manager - INFO - Saved 199 candles for FLOUSD_otc
2025-08-02 03:31:05,515 - src.historical_data.historical_manager - INFO - Updated historical data for FLOUSD_otc
2025-08-02 03:31:07,523 - src.historical_data.historical_manager - INFO - Updating historical data for FTSGBP
2025-08-02 03:31:07,524 - src.historical_data.historical_manager - INFO - No existing data for FTSGBP, fetching 30 days
2025-08-02 03:31:07,524 - src.historical_data.historical_manager - INFO - Fetching historical data for FTSGBP (30 days)
2025-08-02 03:31:07,880 - src.historical_data.historical_manager - INFO - Fetched 199 candles for FTSGBP
2025-08-02 03:31:07,880 - src.historical_data.historical_manager - INFO - Saved 199 candles for FTSGBP
2025-08-02 03:31:07,880 - src.historical_data.historical_manager - INFO - Updated historical data for FTSGBP
2025-08-02 03:31:07,936 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:31:07,937 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:31:07,937 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:31:07,940 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:31:07,940 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:31:09,886 - src.historical_data.historical_manager - INFO - Updating historical data for GALUSD_otc
2025-08-02 03:31:09,886 - src.historical_data.historical_manager - INFO - No existing data for GALUSD_otc, fetching 30 days
2025-08-02 03:31:09,886 - src.historical_data.historical_manager - INFO - Fetching historical data for GALUSD_otc (30 days)
2025-08-02 03:31:10,221 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GALUSD_otc
2025-08-02 03:31:10,228 - src.historical_data.historical_manager - INFO - Saved 199 candles for GALUSD_otc
2025-08-02 03:31:10,228 - src.historical_data.historical_manager - INFO - Updated historical data for GALUSD_otc
2025-08-02 03:31:12,229 - src.historical_data.historical_manager - INFO - Updating historical data for GBPAUD
2025-08-02 03:31:12,232 - src.historical_data.historical_manager - INFO - No existing data for GBPAUD, fetching 30 days
2025-08-02 03:31:12,235 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPAUD (30 days)
2025-08-02 03:31:12,590 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPAUD
2025-08-02 03:31:12,595 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPAUD
2025-08-02 03:31:12,595 - src.historical_data.historical_manager - INFO - Updated historical data for GBPAUD
2025-08-02 03:31:14,611 - src.historical_data.historical_manager - INFO - Updating historical data for GBPAUD_otc
2025-08-02 03:31:14,611 - src.historical_data.historical_manager - INFO - Last data for GBPAUD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:31:14,611 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPAUD_otc (1 days)
2025-08-02 03:31:14,948 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPAUD_otc
2025-08-02 03:31:14,948 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:31:14,955 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPAUD_otc
2025-08-02 03:31:14,955 - src.historical_data.historical_manager - INFO - Updated historical data for GBPAUD_otc
2025-08-02 03:31:16,978 - src.historical_data.historical_manager - INFO - Updating historical data for GBPCAD
2025-08-02 03:31:16,978 - src.historical_data.historical_manager - INFO - No existing data for GBPCAD, fetching 30 days
2025-08-02 03:31:16,978 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPCAD (30 days)
2025-08-02 03:31:17,538 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPCAD
2025-08-02 03:31:17,539 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPCAD
2025-08-02 03:31:17,539 - src.historical_data.historical_manager - INFO - Updated historical data for GBPCAD
2025-08-02 03:31:19,546 - src.historical_data.historical_manager - INFO - Updating historical data for GBPCAD_otc
2025-08-02 03:31:19,548 - src.historical_data.historical_manager - INFO - Last data for GBPCAD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:31:19,548 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPCAD_otc (1 days)
2025-08-02 03:31:19,867 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPCAD_otc
2025-08-02 03:31:19,867 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:31:19,871 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPCAD_otc
2025-08-02 03:31:19,871 - src.historical_data.historical_manager - INFO - Updated historical data for GBPCAD_otc
2025-08-02 03:31:21,876 - src.historical_data.historical_manager - INFO - Updating historical data for GBPCHF
2025-08-02 03:31:21,876 - src.historical_data.historical_manager - INFO - No existing data for GBPCHF, fetching 30 days
2025-08-02 03:31:21,877 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPCHF (30 days)
2025-08-02 03:31:22,206 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPCHF
2025-08-02 03:31:22,207 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPCHF
2025-08-02 03:31:22,207 - src.historical_data.historical_manager - INFO - Updated historical data for GBPCHF
2025-08-02 03:31:24,207 - src.historical_data.historical_manager - INFO - Updating historical data for GBPCHF_otc
2025-08-02 03:31:24,207 - src.historical_data.historical_manager - INFO - Last data for GBPCHF_otc is 0.0 days old, fetching 1 days
2025-08-02 03:31:24,207 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPCHF_otc (1 days)
2025-08-02 03:31:24,540 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPCHF_otc
2025-08-02 03:31:24,540 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:31:24,540 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPCHF_otc
2025-08-02 03:31:24,540 - src.historical_data.historical_manager - INFO - Updated historical data for GBPCHF_otc
2025-08-02 03:31:26,555 - src.historical_data.historical_manager - INFO - Updating historical data for GBPJPY
2025-08-02 03:31:26,555 - src.historical_data.historical_manager - INFO - No existing data for GBPJPY, fetching 30 days
2025-08-02 03:31:26,557 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPJPY (30 days)
2025-08-02 03:31:26,882 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPJPY
2025-08-02 03:31:26,882 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPJPY
2025-08-02 03:31:26,882 - src.historical_data.historical_manager - INFO - Updated historical data for GBPJPY
2025-08-02 03:31:28,374 - websocket - DEBUG - Sending ping
2025-08-02 03:31:28,886 - src.historical_data.historical_manager - INFO - Updating historical data for GBPJPY_otc
2025-08-02 03:31:28,886 - src.historical_data.historical_manager - INFO - Last data for GBPJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:31:28,886 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPJPY_otc (1 days)
2025-08-02 03:31:29,662 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPJPY_otc
2025-08-02 03:31:29,662 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:31:29,662 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPJPY_otc
2025-08-02 03:31:29,662 - src.historical_data.historical_manager - INFO - Updated historical data for GBPJPY_otc
2025-08-02 03:31:31,675 - src.historical_data.historical_manager - INFO - Updating historical data for GBPNZD_otc
2025-08-02 03:31:31,675 - src.historical_data.historical_manager - INFO - No existing data for GBPNZD_otc, fetching 30 days
2025-08-02 03:31:31,675 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPNZD_otc (30 days)
2025-08-02 03:31:31,991 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPNZD_otc
2025-08-02 03:31:31,995 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPNZD_otc
2025-08-02 03:31:31,995 - src.historical_data.historical_manager - INFO - Updated historical data for GBPNZD_otc
2025-08-02 03:31:33,997 - src.historical_data.historical_manager - INFO - Updating historical data for GBPUSD
2025-08-02 03:31:33,997 - src.historical_data.historical_manager - INFO - No existing data for GBPUSD, fetching 30 days
2025-08-02 03:31:33,997 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPUSD (30 days)
2025-08-02 03:31:34,331 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPUSD
2025-08-02 03:31:34,334 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPUSD
2025-08-02 03:31:34,334 - src.historical_data.historical_manager - INFO - Updated historical data for GBPUSD
2025-08-02 03:31:36,354 - src.historical_data.historical_manager - INFO - Updating historical data for GBPUSD_otc
2025-08-02 03:31:36,354 - src.historical_data.historical_manager - INFO - Last data for GBPUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:31:36,354 - src.historical_data.historical_manager - INFO - Fetching historical data for GBPUSD_otc (1 days)
2025-08-02 03:31:36,792 - src.historical_data.historical_manager - INFO - Fetched 199 candles for GBPUSD_otc
2025-08-02 03:31:36,792 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:31:36,800 - src.historical_data.historical_manager - INFO - Saved 199 candles for GBPUSD_otc
2025-08-02 03:31:36,800 - src.historical_data.historical_manager - INFO - Updated historical data for GBPUSD_otc
2025-08-02 03:31:38,802 - src.historical_data.historical_manager - INFO - Updating historical data for HMSUSD_otc
2025-08-02 03:31:38,802 - src.historical_data.historical_manager - INFO - No existing data for HMSUSD_otc, fetching 30 days
2025-08-02 03:31:38,802 - src.historical_data.historical_manager - INFO - Fetching historical data for HMSUSD_otc (30 days)
2025-08-02 03:31:39,126 - src.historical_data.historical_manager - INFO - Fetched 199 candles for HMSUSD_otc
2025-08-02 03:31:39,134 - src.historical_data.historical_manager - INFO - Saved 199 candles for HMSUSD_otc
2025-08-02 03:31:39,134 - src.historical_data.historical_manager - INFO - Updated historical data for HMSUSD_otc
2025-08-02 03:31:41,146 - src.historical_data.historical_manager - INFO - Updating historical data for HSIHKD
2025-08-02 03:31:41,146 - src.historical_data.historical_manager - INFO - No existing data for HSIHKD, fetching 30 days
2025-08-02 03:31:41,146 - src.historical_data.historical_manager - INFO - Fetching historical data for HSIHKD (30 days)
2025-08-02 03:31:41,476 - src.historical_data.historical_manager - INFO - Fetched 199 candles for HSIHKD
2025-08-02 03:31:41,481 - src.historical_data.historical_manager - INFO - Saved 199 candles for HSIHKD
2025-08-02 03:31:41,481 - src.historical_data.historical_manager - INFO - Updated historical data for HSIHKD
2025-08-02 03:31:43,491 - src.historical_data.historical_manager - INFO - Updating historical data for IBXEUR
2025-08-02 03:31:43,492 - src.historical_data.historical_manager - INFO - No existing data for IBXEUR, fetching 30 days
2025-08-02 03:31:43,493 - src.historical_data.historical_manager - INFO - Fetching historical data for IBXEUR (30 days)
2025-08-02 03:31:43,817 - src.historical_data.historical_manager - INFO - Fetched 199 candles for IBXEUR
2025-08-02 03:31:43,817 - src.historical_data.historical_manager - INFO - Saved 199 candles for IBXEUR
2025-08-02 03:31:43,817 - src.historical_data.historical_manager - INFO - Updated historical data for IBXEUR
2025-08-02 03:31:45,819 - src.historical_data.historical_manager - INFO - Updating historical data for JPXJPY
2025-08-02 03:31:45,819 - src.historical_data.historical_manager - INFO - No existing data for JPXJPY, fetching 30 days
2025-08-02 03:31:45,819 - src.historical_data.historical_manager - INFO - Fetching historical data for JPXJPY (30 days)
2025-08-02 03:31:46,135 - src.historical_data.historical_manager - INFO - Fetched 199 candles for JPXJPY
2025-08-02 03:31:46,135 - src.historical_data.historical_manager - INFO - Saved 199 candles for JPXJPY
2025-08-02 03:31:46,144 - src.historical_data.historical_manager - INFO - Updated historical data for JPXJPY
2025-08-02 03:31:48,163 - src.historical_data.historical_manager - INFO - Updating historical data for LINUSD_otc
2025-08-02 03:31:48,163 - src.historical_data.historical_manager - INFO - No existing data for LINUSD_otc, fetching 30 days
2025-08-02 03:31:48,164 - src.historical_data.historical_manager - INFO - Fetching historical data for LINUSD_otc (30 days)
2025-08-02 03:31:48,496 - src.historical_data.historical_manager - INFO - Fetched 199 candles for LINUSD_otc
2025-08-02 03:31:48,505 - src.historical_data.historical_manager - INFO - Saved 199 candles for LINUSD_otc
2025-08-02 03:31:48,505 - src.historical_data.historical_manager - INFO - Updated historical data for LINUSD_otc
2025-08-02 03:31:50,515 - src.historical_data.historical_manager - INFO - Updating historical data for LTCUSD_otc
2025-08-02 03:31:50,516 - src.historical_data.historical_manager - INFO - No existing data for LTCUSD_otc, fetching 30 days
2025-08-02 03:31:50,516 - src.historical_data.historical_manager - INFO - Fetching historical data for LTCUSD_otc (30 days)
2025-08-02 03:31:50,841 - src.historical_data.historical_manager - INFO - Fetched 199 candles for LTCUSD_otc
2025-08-02 03:31:50,841 - src.historical_data.historical_manager - INFO - Saved 199 candles for LTCUSD_otc
2025-08-02 03:31:50,841 - src.historical_data.historical_manager - INFO - Updated historical data for LTCUSD_otc
2025-08-02 03:31:52,384 - websocket - DEBUG - Sending ping
2025-08-02 03:31:52,841 - src.historical_data.historical_manager - INFO - Updating historical data for MANUSD_otc
2025-08-02 03:31:52,841 - src.historical_data.historical_manager - INFO - No existing data for MANUSD_otc, fetching 30 days
2025-08-02 03:31:52,841 - src.historical_data.historical_manager - INFO - Fetching historical data for MANUSD_otc (30 days)
2025-08-02 03:31:53,175 - src.historical_data.historical_manager - INFO - Fetched 199 candles for MANUSD_otc
2025-08-02 03:31:53,183 - src.historical_data.historical_manager - INFO - Saved 199 candles for MANUSD_otc
2025-08-02 03:31:53,183 - src.historical_data.historical_manager - INFO - Updated historical data for MANUSD_otc
2025-08-02 03:31:55,183 - src.historical_data.historical_manager - INFO - Updating historical data for MELUSD_otc
2025-08-02 03:31:55,183 - src.historical_data.historical_manager - INFO - No existing data for MELUSD_otc, fetching 30 days
2025-08-02 03:31:55,183 - src.historical_data.historical_manager - INFO - Fetching historical data for MELUSD_otc (30 days)
2025-08-02 03:31:55,510 - src.historical_data.historical_manager - INFO - Fetched 199 candles for MELUSD_otc
2025-08-02 03:31:55,510 - src.historical_data.historical_manager - INFO - Saved 199 candles for MELUSD_otc
2025-08-02 03:31:55,510 - src.historical_data.historical_manager - INFO - Updated historical data for MELUSD_otc
2025-08-02 03:31:57,520 - src.historical_data.historical_manager - INFO - Updating historical data for NDXUSD
2025-08-02 03:31:57,520 - src.historical_data.historical_manager - INFO - No existing data for NDXUSD, fetching 30 days
2025-08-02 03:31:57,522 - src.historical_data.historical_manager - INFO - Fetching historical data for NDXUSD (30 days)
2025-08-02 03:31:57,851 - src.historical_data.historical_manager - INFO - Fetched 199 candles for NDXUSD
2025-08-02 03:31:57,857 - src.historical_data.historical_manager - INFO - Saved 199 candles for NDXUSD
2025-08-02 03:31:57,857 - src.historical_data.historical_manager - INFO - Updated historical data for NDXUSD
2025-08-02 03:31:59,864 - src.historical_data.historical_manager - INFO - Updating historical data for NOTUSD_otc
2025-08-02 03:31:59,865 - src.historical_data.historical_manager - INFO - No existing data for NOTUSD_otc, fetching 30 days
2025-08-02 03:31:59,865 - src.historical_data.historical_manager - INFO - Fetching historical data for NOTUSD_otc (30 days)
2025-08-02 03:32:00,191 - src.historical_data.historical_manager - INFO - Fetched 199 candles for NOTUSD_otc
2025-08-02 03:32:00,197 - src.historical_data.historical_manager - INFO - Saved 199 candles for NOTUSD_otc
2025-08-02 03:32:00,199 - src.historical_data.historical_manager - INFO - Updated historical data for NOTUSD_otc
2025-08-02 03:32:02,210 - src.historical_data.historical_manager - INFO - Updating historical data for NZDCAD_otc
2025-08-02 03:32:02,212 - src.historical_data.historical_manager - INFO - No existing data for NZDCAD_otc, fetching 30 days
2025-08-02 03:32:02,212 - src.historical_data.historical_manager - INFO - Fetching historical data for NZDCAD_otc (30 days)
2025-08-02 03:32:03,841 - src.historical_data.historical_manager - INFO - Fetched 199 candles for NZDCAD_otc
2025-08-02 03:32:03,848 - src.historical_data.historical_manager - INFO - Saved 199 candles for NZDCAD_otc
2025-08-02 03:32:03,851 - src.historical_data.historical_manager - INFO - Updated historical data for NZDCAD_otc
2025-08-02 03:32:05,855 - src.historical_data.historical_manager - INFO - Updating historical data for NZDCHF_otc
2025-08-02 03:32:05,855 - src.historical_data.historical_manager - INFO - No existing data for NZDCHF_otc, fetching 30 days
2025-08-02 03:32:05,855 - src.historical_data.historical_manager - INFO - Fetching historical data for NZDCHF_otc (30 days)
2025-08-02 03:32:06,172 - src.historical_data.historical_manager - INFO - Fetched 199 candles for NZDCHF_otc
2025-08-02 03:32:06,178 - src.historical_data.historical_manager - INFO - Saved 199 candles for NZDCHF_otc
2025-08-02 03:32:06,179 - src.historical_data.historical_manager - INFO - Updated historical data for NZDCHF_otc
2025-08-02 03:32:07,937 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:32:07,937 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:32:07,939 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:32:07,941 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:32:07,941 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:32:08,200 - src.historical_data.historical_manager - INFO - Updating historical data for NZDJPY_otc
2025-08-02 03:32:08,200 - src.historical_data.historical_manager - INFO - No existing data for NZDJPY_otc, fetching 30 days
2025-08-02 03:32:08,202 - src.historical_data.historical_manager - INFO - Fetching historical data for NZDJPY_otc (30 days)
2025-08-02 03:32:08,533 - src.historical_data.historical_manager - INFO - Fetched 199 candles for NZDJPY_otc
2025-08-02 03:32:08,536 - src.historical_data.historical_manager - INFO - Saved 199 candles for NZDJPY_otc
2025-08-02 03:32:08,536 - src.historical_data.historical_manager - INFO - Updated historical data for NZDJPY_otc
2025-08-02 03:32:10,537 - src.historical_data.historical_manager - INFO - Updating historical data for NZDUSD_otc
2025-08-02 03:32:10,538 - src.historical_data.historical_manager - INFO - No existing data for NZDUSD_otc, fetching 30 days
2025-08-02 03:32:10,538 - src.historical_data.historical_manager - INFO - Fetching historical data for NZDUSD_otc (30 days)
2025-08-02 03:32:10,860 - src.historical_data.historical_manager - INFO - Fetched 199 candles for NZDUSD_otc
2025-08-02 03:32:10,860 - src.historical_data.historical_manager - INFO - Saved 199 candles for NZDUSD_otc
2025-08-02 03:32:10,860 - src.historical_data.historical_manager - INFO - Updated historical data for NZDUSD_otc
2025-08-02 03:32:12,862 - src.historical_data.historical_manager - INFO - Updating historical data for PEPUSD_otc
2025-08-02 03:32:12,864 - src.historical_data.historical_manager - INFO - No existing data for PEPUSD_otc, fetching 30 days
2025-08-02 03:32:12,864 - src.historical_data.historical_manager - INFO - Fetching historical data for PEPUSD_otc (30 days)
2025-08-02 03:32:13,644 - src.historical_data.historical_manager - INFO - Fetched 199 candles for PEPUSD_otc
2025-08-02 03:32:13,644 - src.historical_data.historical_manager - INFO - Saved 199 candles for PEPUSD_otc
2025-08-02 03:32:13,644 - src.historical_data.historical_manager - INFO - Updated historical data for PEPUSD_otc
2025-08-02 03:32:15,648 - src.historical_data.historical_manager - INFO - Updating historical data for SHIUSD_otc
2025-08-02 03:32:15,648 - src.historical_data.historical_manager - INFO - No existing data for SHIUSD_otc, fetching 30 days
2025-08-02 03:32:15,648 - src.historical_data.historical_manager - INFO - Fetching historical data for SHIUSD_otc (30 days)
2025-08-02 03:32:15,971 - src.historical_data.historical_manager - INFO - Fetched 199 candles for SHIUSD_otc
2025-08-02 03:32:15,972 - src.historical_data.historical_manager - INFO - Saved 199 candles for SHIUSD_otc
2025-08-02 03:32:15,972 - src.historical_data.historical_manager - INFO - Updated historical data for SHIUSD_otc
2025-08-02 03:32:16,394 - websocket - DEBUG - Sending ping
2025-08-02 03:32:17,980 - src.historical_data.historical_manager - INFO - Updating historical data for SOLUSD_otc
2025-08-02 03:32:17,980 - src.historical_data.historical_manager - INFO - No existing data for SOLUSD_otc, fetching 30 days
2025-08-02 03:32:17,980 - src.historical_data.historical_manager - INFO - Fetching historical data for SOLUSD_otc (30 days)
2025-08-02 03:32:18,320 - src.historical_data.historical_manager - INFO - Fetched 199 candles for SOLUSD_otc
2025-08-02 03:32:18,322 - src.historical_data.historical_manager - INFO - Saved 199 candles for SOLUSD_otc
2025-08-02 03:32:18,322 - src.historical_data.historical_manager - INFO - Updated historical data for SOLUSD_otc
2025-08-02 03:32:20,342 - src.historical_data.historical_manager - INFO - Updating historical data for STXEUR
2025-08-02 03:32:20,346 - src.historical_data.historical_manager - INFO - No existing data for STXEUR, fetching 30 days
2025-08-02 03:32:20,346 - src.historical_data.historical_manager - INFO - Fetching historical data for STXEUR (30 days)
2025-08-02 03:32:20,897 - src.historical_data.historical_manager - INFO - Fetched 199 candles for STXEUR
2025-08-02 03:32:20,905 - src.historical_data.historical_manager - INFO - Saved 199 candles for STXEUR
2025-08-02 03:32:20,905 - src.historical_data.historical_manager - INFO - Updated historical data for STXEUR
2025-08-02 03:32:22,916 - src.historical_data.historical_manager - INFO - Updating historical data for TIAUSD_otc
2025-08-02 03:32:22,916 - src.historical_data.historical_manager - INFO - No existing data for TIAUSD_otc, fetching 30 days
2025-08-02 03:32:22,916 - src.historical_data.historical_manager - INFO - Fetching historical data for TIAUSD_otc (30 days)
2025-08-02 03:32:23,244 - src.historical_data.historical_manager - INFO - Fetched 199 candles for TIAUSD_otc
2025-08-02 03:32:23,244 - src.historical_data.historical_manager - INFO - Saved 199 candles for TIAUSD_otc
2025-08-02 03:32:23,244 - src.historical_data.historical_manager - INFO - Updated historical data for TIAUSD_otc
2025-08-02 03:32:25,246 - src.historical_data.historical_manager - INFO - Updating historical data for TONUSD_otc
2025-08-02 03:32:25,246 - src.historical_data.historical_manager - INFO - No existing data for TONUSD_otc, fetching 30 days
2025-08-02 03:32:25,248 - src.historical_data.historical_manager - INFO - Fetching historical data for TONUSD_otc (30 days)
2025-08-02 03:32:25,574 - src.historical_data.historical_manager - INFO - Fetched 199 candles for TONUSD_otc
2025-08-02 03:32:25,580 - src.historical_data.historical_manager - INFO - Saved 199 candles for TONUSD_otc
2025-08-02 03:32:25,580 - src.historical_data.historical_manager - INFO - Updated historical data for TONUSD_otc
2025-08-02 03:32:27,581 - src.historical_data.historical_manager - INFO - Updating historical data for TRUUSD_otc
2025-08-02 03:32:27,581 - src.historical_data.historical_manager - INFO - No existing data for TRUUSD_otc, fetching 30 days
2025-08-02 03:32:27,581 - src.historical_data.historical_manager - INFO - Fetching historical data for TRUUSD_otc (30 days)
2025-08-02 03:32:27,902 - src.historical_data.historical_manager - INFO - Fetched 199 candles for TRUUSD_otc
2025-08-02 03:32:27,910 - src.historical_data.historical_manager - INFO - Saved 199 candles for TRUUSD_otc
2025-08-02 03:32:27,910 - src.historical_data.historical_manager - INFO - Updated historical data for TRUUSD_otc
2025-08-02 03:32:29,917 - src.historical_data.historical_manager - INFO - Updating historical data for TRXUSD_otc
2025-08-02 03:32:29,917 - src.historical_data.historical_manager - INFO - No existing data for TRXUSD_otc, fetching 30 days
2025-08-02 03:32:29,917 - src.historical_data.historical_manager - INFO - Fetching historical data for TRXUSD_otc (30 days)
2025-08-02 03:32:30,234 - src.historical_data.historical_manager - INFO - Fetched 199 candles for TRXUSD_otc
2025-08-02 03:32:30,239 - src.historical_data.historical_manager - INFO - Saved 199 candles for TRXUSD_otc
2025-08-02 03:32:30,241 - src.historical_data.historical_manager - INFO - Updated historical data for TRXUSD_otc
2025-08-02 03:32:32,256 - src.historical_data.historical_manager - INFO - Updating historical data for USDARS_otc
2025-08-02 03:32:32,256 - src.historical_data.historical_manager - INFO - No existing data for USDARS_otc, fetching 30 days
2025-08-02 03:32:32,256 - src.historical_data.historical_manager - INFO - Fetching historical data for USDARS_otc (30 days)
2025-08-02 03:32:33,446 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDARS_otc
2025-08-02 03:32:33,454 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDARS_otc
2025-08-02 03:32:33,454 - src.historical_data.historical_manager - INFO - Updated historical data for USDARS_otc
2025-08-02 03:32:35,466 - src.historical_data.historical_manager - INFO - Updating historical data for USDBDT_otc
2025-08-02 03:32:35,466 - src.historical_data.historical_manager - INFO - No existing data for USDBDT_otc, fetching 30 days
2025-08-02 03:32:35,466 - src.historical_data.historical_manager - INFO - Fetching historical data for USDBDT_otc (30 days)
2025-08-02 03:32:35,793 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDBDT_otc
2025-08-02 03:32:35,796 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDBDT_otc
2025-08-02 03:32:35,796 - src.historical_data.historical_manager - INFO - Updated historical data for USDBDT_otc
2025-08-02 03:32:37,796 - src.historical_data.historical_manager - INFO - Updating historical data for USDCAD
2025-08-02 03:32:37,796 - src.historical_data.historical_manager - INFO - No existing data for USDCAD, fetching 30 days
2025-08-02 03:32:37,796 - src.historical_data.historical_manager - INFO - Fetching historical data for USDCAD (30 days)
2025-08-02 03:32:38,103 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDCAD
2025-08-02 03:32:38,103 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDCAD
2025-08-02 03:32:38,103 - src.historical_data.historical_manager - INFO - Updated historical data for USDCAD
2025-08-02 03:32:40,110 - src.historical_data.historical_manager - INFO - Updating historical data for USDCAD_otc
2025-08-02 03:32:40,110 - src.historical_data.historical_manager - INFO - Last data for USDCAD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:32:40,110 - src.historical_data.historical_manager - INFO - Fetching historical data for USDCAD_otc (1 days)
2025-08-02 03:32:40,404 - websocket - DEBUG - Sending ping
2025-08-02 03:32:41,548 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDCAD_otc
2025-08-02 03:32:41,550 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:32:41,557 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDCAD_otc
2025-08-02 03:32:41,561 - src.historical_data.historical_manager - INFO - Updated historical data for USDCAD_otc
2025-08-02 03:32:43,570 - src.historical_data.historical_manager - INFO - Updating historical data for USDCHF
2025-08-02 03:32:43,570 - src.historical_data.historical_manager - INFO - No existing data for USDCHF, fetching 30 days
2025-08-02 03:32:43,570 - src.historical_data.historical_manager - INFO - Fetching historical data for USDCHF (30 days)
2025-08-02 03:32:43,908 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDCHF
2025-08-02 03:32:43,915 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDCHF
2025-08-02 03:32:43,915 - src.historical_data.historical_manager - INFO - Updated historical data for USDCHF
2025-08-02 03:32:45,927 - src.historical_data.historical_manager - INFO - Updating historical data for USDCHF_otc
2025-08-02 03:32:45,927 - src.historical_data.historical_manager - INFO - Last data for USDCHF_otc is 0.0 days old, fetching 1 days
2025-08-02 03:32:45,927 - src.historical_data.historical_manager - INFO - Fetching historical data for USDCHF_otc (1 days)
2025-08-02 03:32:46,252 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDCHF_otc
2025-08-02 03:32:46,252 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:32:46,260 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDCHF_otc
2025-08-02 03:32:46,260 - src.historical_data.historical_manager - INFO - Updated historical data for USDCHF_otc
2025-08-02 03:32:48,262 - src.historical_data.historical_manager - INFO - Updating historical data for USDCOP_otc
2025-08-02 03:32:48,311 - src.historical_data.historical_manager - INFO - No existing data for USDCOP_otc, fetching 30 days
2025-08-02 03:32:48,315 - src.historical_data.historical_manager - INFO - Fetching historical data for USDCOP_otc (30 days)
2025-08-02 03:32:48,763 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDCOP_otc
2025-08-02 03:32:48,764 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDCOP_otc
2025-08-02 03:32:48,764 - src.historical_data.historical_manager - INFO - Updated historical data for USDCOP_otc
2025-08-02 03:32:50,767 - src.historical_data.historical_manager - INFO - Updating historical data for USDDZD_otc
2025-08-02 03:32:50,767 - src.historical_data.historical_manager - INFO - No existing data for USDDZD_otc, fetching 30 days
2025-08-02 03:32:50,767 - src.historical_data.historical_manager - INFO - Fetching historical data for USDDZD_otc (30 days)
2025-08-02 03:32:51,200 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDDZD_otc
2025-08-02 03:32:51,200 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDDZD_otc
2025-08-02 03:32:51,200 - src.historical_data.historical_manager - INFO - Updated historical data for USDDZD_otc
2025-08-02 03:32:53,217 - src.historical_data.historical_manager - INFO - Updating historical data for USDEGP_otc
2025-08-02 03:32:53,217 - src.historical_data.historical_manager - INFO - No existing data for USDEGP_otc, fetching 30 days
2025-08-02 03:32:53,217 - src.historical_data.historical_manager - INFO - Fetching historical data for USDEGP_otc (30 days)
2025-08-02 03:32:54,306 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDEGP_otc
2025-08-02 03:32:54,306 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDEGP_otc
2025-08-02 03:32:54,306 - src.historical_data.historical_manager - INFO - Updated historical data for USDEGP_otc
2025-08-02 03:32:56,306 - src.historical_data.historical_manager - INFO - Updating historical data for USDIDR_otc
2025-08-02 03:32:56,306 - src.historical_data.historical_manager - INFO - No existing data for USDIDR_otc, fetching 30 days
2025-08-02 03:32:56,306 - src.historical_data.historical_manager - INFO - Fetching historical data for USDIDR_otc (30 days)
2025-08-02 03:32:56,626 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDIDR_otc
2025-08-02 03:32:56,633 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDIDR_otc
2025-08-02 03:32:56,633 - src.historical_data.historical_manager - INFO - Updated historical data for USDIDR_otc
2025-08-02 03:32:58,638 - src.historical_data.historical_manager - INFO - Updating historical data for USDINR_otc
2025-08-02 03:32:58,638 - src.historical_data.historical_manager - INFO - No existing data for USDINR_otc, fetching 30 days
2025-08-02 03:32:58,638 - src.historical_data.historical_manager - INFO - Fetching historical data for USDINR_otc (30 days)
2025-08-02 03:32:58,965 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDINR_otc
2025-08-02 03:32:58,970 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDINR_otc
2025-08-02 03:32:58,970 - src.historical_data.historical_manager - INFO - Updated historical data for USDINR_otc
2025-08-02 03:33:00,976 - src.historical_data.historical_manager - INFO - Updating historical data for USDJPY
2025-08-02 03:33:00,979 - src.historical_data.historical_manager - INFO - No existing data for USDJPY, fetching 30 days
2025-08-02 03:33:00,979 - src.historical_data.historical_manager - INFO - Fetching historical data for USDJPY (30 days)
2025-08-02 03:33:01,537 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDJPY
2025-08-02 03:33:01,539 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDJPY
2025-08-02 03:33:01,539 - src.historical_data.historical_manager - INFO - Updated historical data for USDJPY
2025-08-02 03:33:03,546 - src.historical_data.historical_manager - INFO - Updating historical data for USDJPY_otc
2025-08-02 03:33:03,546 - src.historical_data.historical_manager - INFO - Last data for USDJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:33:03,546 - src.historical_data.historical_manager - INFO - Fetching historical data for USDJPY_otc (1 days)
2025-08-02 03:33:04,418 - websocket - DEBUG - Sending ping
2025-08-02 03:33:04,424 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDJPY_otc
2025-08-02 03:33:04,443 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:33:04,447 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDJPY_otc
2025-08-02 03:33:04,447 - src.historical_data.historical_manager - INFO - Updated historical data for USDJPY_otc
2025-08-02 03:33:06,448 - src.historical_data.historical_manager - INFO - Updating historical data for USDMXN_otc
2025-08-02 03:33:06,448 - src.historical_data.historical_manager - INFO - No existing data for USDMXN_otc, fetching 30 days
2025-08-02 03:33:06,448 - src.historical_data.historical_manager - INFO - Fetching historical data for USDMXN_otc (30 days)
2025-08-02 03:33:07,100 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDMXN_otc
2025-08-02 03:33:07,111 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDMXN_otc
2025-08-02 03:33:07,111 - src.historical_data.historical_manager - INFO - Updated historical data for USDMXN_otc
2025-08-02 03:33:07,951 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:33:07,951 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:33:07,951 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:33:07,956 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:33:07,956 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:33:09,113 - src.historical_data.historical_manager - INFO - Updating historical data for USDNGN_otc
2025-08-02 03:33:09,113 - src.historical_data.historical_manager - INFO - No existing data for USDNGN_otc, fetching 30 days
2025-08-02 03:33:09,113 - src.historical_data.historical_manager - INFO - Fetching historical data for USDNGN_otc (30 days)
2025-08-02 03:33:09,430 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDNGN_otc
2025-08-02 03:33:09,435 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDNGN_otc
2025-08-02 03:33:09,437 - src.historical_data.historical_manager - INFO - Updated historical data for USDNGN_otc
2025-08-02 03:33:11,438 - src.historical_data.historical_manager - INFO - Updating historical data for USDPHP_otc
2025-08-02 03:33:11,439 - src.historical_data.historical_manager - INFO - No existing data for USDPHP_otc, fetching 30 days
2025-08-02 03:33:11,440 - src.historical_data.historical_manager - INFO - Fetching historical data for USDPHP_otc (30 days)
2025-08-02 03:33:11,750 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDPHP_otc
2025-08-02 03:33:11,750 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDPHP_otc
2025-08-02 03:33:11,750 - src.historical_data.historical_manager - INFO - Updated historical data for USDPHP_otc
2025-08-02 03:33:13,757 - src.historical_data.historical_manager - INFO - Updating historical data for USDPKR_otc
2025-08-02 03:33:13,757 - src.historical_data.historical_manager - INFO - No existing data for USDPKR_otc, fetching 30 days
2025-08-02 03:33:13,757 - src.historical_data.historical_manager - INFO - Fetching historical data for USDPKR_otc (30 days)
2025-08-02 03:33:14,625 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDPKR_otc
2025-08-02 03:33:14,633 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDPKR_otc
2025-08-02 03:33:14,634 - src.historical_data.historical_manager - INFO - Updated historical data for USDPKR_otc
2025-08-02 03:33:16,642 - src.historical_data.historical_manager - INFO - Updating historical data for USDTRY_otc
2025-08-02 03:33:16,642 - src.historical_data.historical_manager - INFO - No existing data for USDTRY_otc, fetching 30 days
2025-08-02 03:33:16,642 - src.historical_data.historical_manager - INFO - Fetching historical data for USDTRY_otc (30 days)
2025-08-02 03:33:16,963 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDTRY_otc
2025-08-02 03:33:16,963 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDTRY_otc
2025-08-02 03:33:16,963 - src.historical_data.historical_manager - INFO - Updated historical data for USDTRY_otc
2025-08-02 03:33:18,965 - src.historical_data.historical_manager - INFO - Updating historical data for USDZAR_otc
2025-08-02 03:33:18,965 - src.historical_data.historical_manager - INFO - No existing data for USDZAR_otc, fetching 30 days
2025-08-02 03:33:18,965 - src.historical_data.historical_manager - INFO - Fetching historical data for USDZAR_otc (30 days)
2025-08-02 03:33:19,281 - src.historical_data.historical_manager - INFO - Fetched 199 candles for USDZAR_otc
2025-08-02 03:33:19,283 - src.historical_data.historical_manager - INFO - Saved 199 candles for USDZAR_otc
2025-08-02 03:33:19,283 - src.historical_data.historical_manager - INFO - Updated historical data for USDZAR_otc
2025-08-02 03:33:21,289 - src.historical_data.historical_manager - INFO - Updating historical data for WIFUSD_otc
2025-08-02 03:33:21,289 - src.historical_data.historical_manager - INFO - No existing data for WIFUSD_otc, fetching 30 days
2025-08-02 03:33:21,289 - src.historical_data.historical_manager - INFO - Fetching historical data for WIFUSD_otc (30 days)
2025-08-02 03:33:22,938 - src.historical_data.historical_manager - INFO - Fetched 199 candles for WIFUSD_otc
2025-08-02 03:33:22,946 - src.historical_data.historical_manager - INFO - Saved 199 candles for WIFUSD_otc
2025-08-02 03:33:22,946 - src.historical_data.historical_manager - INFO - Updated historical data for WIFUSD_otc
2025-08-02 03:33:24,952 - src.historical_data.historical_manager - INFO - Updating historical data for XAGUSD
2025-08-02 03:33:24,952 - src.historical_data.historical_manager - INFO - No existing data for XAGUSD, fetching 30 days
2025-08-02 03:33:24,952 - src.historical_data.historical_manager - INFO - Fetching historical data for XAGUSD (30 days)
2025-08-02 03:33:27,849 - src.historical_data.historical_manager - INFO - Fetched 199 candles for XAGUSD
2025-08-02 03:33:27,855 - src.historical_data.historical_manager - INFO - Saved 199 candles for XAGUSD
2025-08-02 03:33:27,856 - src.historical_data.historical_manager - INFO - Updated historical data for XAGUSD
2025-08-02 03:33:28,432 - websocket - DEBUG - Sending ping
2025-08-02 03:33:29,870 - src.historical_data.historical_manager - INFO - Updating historical data for XAGUSD_otc
2025-08-02 03:33:29,878 - src.historical_data.historical_manager - INFO - Last data for XAGUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:33:29,879 - src.historical_data.historical_manager - INFO - Fetching historical data for XAGUSD_otc (1 days)
2025-08-02 03:33:30,234 - src.historical_data.historical_manager - INFO - Fetched 199 candles for XAGUSD_otc
2025-08-02 03:33:30,234 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:33:30,241 - src.historical_data.historical_manager - INFO - Saved 199 candles for XAGUSD_otc
2025-08-02 03:33:30,243 - src.historical_data.historical_manager - INFO - Updated historical data for XAGUSD_otc
2025-08-02 03:33:32,257 - src.historical_data.historical_manager - INFO - Updating historical data for XAUUSD
2025-08-02 03:33:32,257 - src.historical_data.historical_manager - INFO - No existing data for XAUUSD, fetching 30 days
2025-08-02 03:33:32,257 - src.historical_data.historical_manager - INFO - Fetching historical data for XAUUSD (30 days)
2025-08-02 03:33:32,898 - src.historical_data.historical_manager - INFO - Fetched 199 candles for XAUUSD
2025-08-02 03:33:32,903 - src.historical_data.historical_manager - INFO - Saved 199 candles for XAUUSD
2025-08-02 03:33:32,906 - src.historical_data.historical_manager - INFO - Updated historical data for XAUUSD
2025-08-02 03:33:34,922 - src.historical_data.historical_manager - INFO - Updating historical data for XAUUSD_otc
2025-08-02 03:33:34,922 - src.historical_data.historical_manager - INFO - Last data for XAUUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:33:34,922 - src.historical_data.historical_manager - INFO - Fetching historical data for XAUUSD_otc (1 days)
2025-08-02 03:33:35,243 - src.historical_data.historical_manager - INFO - Fetched 199 candles for XAUUSD_otc
2025-08-02 03:33:35,243 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 199 existing candles
2025-08-02 03:33:35,248 - src.historical_data.historical_manager - INFO - Saved 199 candles for XAUUSD_otc
2025-08-02 03:33:35,251 - src.historical_data.historical_manager - INFO - Updated historical data for XAUUSD_otc
2025-08-02 03:33:37,266 - src.historical_data.historical_manager - INFO - Updating historical data for XRPUSD_otc
2025-08-02 03:33:37,266 - src.historical_data.historical_manager - INFO - No existing data for XRPUSD_otc, fetching 30 days
2025-08-02 03:33:37,266 - src.historical_data.historical_manager - INFO - Fetching historical data for XRPUSD_otc (30 days)
2025-08-02 03:33:37,584 - src.historical_data.historical_manager - INFO - Fetched 199 candles for XRPUSD_otc
2025-08-02 03:33:37,589 - src.historical_data.historical_manager - INFO - Saved 199 candles for XRPUSD_otc
2025-08-02 03:33:37,589 - src.historical_data.historical_manager - INFO - Updated historical data for XRPUSD_otc
2025-08-02 03:33:39,613 - src.historical_data.historical_manager - INFO - Updating historical data for ZECUSD_otc
2025-08-02 03:33:39,617 - src.historical_data.historical_manager - INFO - No existing data for ZECUSD_otc, fetching 30 days
2025-08-02 03:33:39,630 - src.historical_data.historical_manager - INFO - Fetching historical data for ZECUSD_otc (30 days)
2025-08-02 03:33:39,953 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ZECUSD_otc
2025-08-02 03:33:39,960 - src.historical_data.historical_manager - INFO - Saved 199 candles for ZECUSD_otc
2025-08-02 03:33:39,960 - src.historical_data.historical_manager - INFO - Updated historical data for ZECUSD_otc
2025-08-02 03:33:41,970 - src.historical_data.historical_manager - INFO - Historical data update complete. Next update in 300 seconds
2025-08-02 03:33:52,442 - websocket - DEBUG - Sending ping
2025-08-02 03:34:07,968 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:34:07,968 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:34:07,968 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:34:07,973 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:34:07,975 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:34:16,447 - websocket - DEBUG - Sending ping
2025-08-02 03:34:40,456 - websocket - DEBUG - Sending ping
2025-08-02 03:34:57,651 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:35:04,459 - websocket - DEBUG - Sending ping
2025-08-02 03:35:07,975 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:35:07,977 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:35:07,977 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:35:07,983 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:35:07,983 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:35:28,468 - websocket - DEBUG - Sending ping
2025-08-02 03:35:52,482 - websocket - DEBUG - Sending ping
2025-08-02 03:36:07,995 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:36:07,995 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:36:07,995 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:36:07,995 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:36:07,995 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:36:10,552 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:36:16,492 - websocket - DEBUG - Sending ping
2025-08-02 03:36:40,500 - websocket - DEBUG - Sending ping
2025-08-02 03:37:04,507 - websocket - DEBUG - Sending ping
2025-08-02 03:37:08,015 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:37:08,015 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:37:08,018 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:37:08,023 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:37:08,024 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:37:28,517 - websocket - DEBUG - Sending ping
2025-08-02 03:37:52,525 - websocket - DEBUG - Sending ping
2025-08-02 03:38:08,032 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:38:08,033 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:38:08,033 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:38:08,033 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:38:08,033 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:38:16,542 - websocket - DEBUG - Sending ping
2025-08-02 03:38:29,606 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:30,054 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:30,589 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:30,894 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,395 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,427 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,445 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,482 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,516 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,546 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,576 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,599 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,632 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,663 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:31,923 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,430 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,461 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,496 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,519 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,552 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,584 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,615 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,645 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,667 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,700 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,739 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,763 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,796 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,818 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,850 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,879 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,913 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,946 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:32,968 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,001 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,031 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,063 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,096 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,125 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,154 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,183 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,212 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,252 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,272 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,316 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,337 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,365 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,397 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,428 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,455 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,482 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,517 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,546 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,578 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,603 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,636 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,662 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,697 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,718 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,752 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,782 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,817 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,849 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,879 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,908 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:33,933 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:38:40,552 - websocket - DEBUG - Sending ping
2025-08-02 03:38:41,967 - src.historical_data.historical_manager - INFO - Updating historical data for 110 forex pairs
2025-08-02 03:38:41,967 - src.historical_data.historical_manager - INFO - Updating historical data for ADAUSD_otc
2025-08-02 03:38:41,971 - src.historical_data.historical_manager - INFO - Last data for ADAUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:38:41,972 - src.historical_data.historical_manager - INFO - Fetching historical data for ADAUSD_otc (1 days)
2025-08-02 03:38:44,140 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ADAUSD_otc
2025-08-02 03:38:44,141 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:38:44,143 - src.historical_data.historical_manager - INFO - Saved 201 candles for ADAUSD_otc
2025-08-02 03:38:44,143 - src.historical_data.historical_manager - INFO - Updated historical data for ADAUSD_otc
2025-08-02 03:38:46,164 - src.historical_data.historical_manager - INFO - Updating historical data for APTUSD_otc
2025-08-02 03:38:46,164 - src.historical_data.historical_manager - INFO - Last data for APTUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:38:46,167 - src.historical_data.historical_manager - INFO - Fetching historical data for APTUSD_otc (1 days)
2025-08-02 03:38:48,998 - src.historical_data.historical_manager - INFO - Fetched 199 candles for APTUSD_otc
2025-08-02 03:38:48,998 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:38:49,001 - src.historical_data.historical_manager - INFO - Saved 201 candles for APTUSD_otc
2025-08-02 03:38:49,001 - src.historical_data.historical_manager - INFO - Updated historical data for APTUSD_otc
2025-08-02 03:38:51,006 - src.historical_data.historical_manager - INFO - Updating historical data for ARBUSD_otc
2025-08-02 03:38:51,006 - src.historical_data.historical_manager - INFO - Last data for ARBUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:38:51,006 - src.historical_data.historical_manager - INFO - Fetching historical data for ARBUSD_otc (1 days)
2025-08-02 03:38:52,531 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ARBUSD_otc
2025-08-02 03:38:52,531 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:38:52,531 - src.historical_data.historical_manager - INFO - Saved 201 candles for ARBUSD_otc
2025-08-02 03:38:52,531 - src.historical_data.historical_manager - INFO - Updated historical data for ARBUSD_otc
2025-08-02 03:38:54,537 - src.historical_data.historical_manager - INFO - Updating historical data for ATOUSD_otc
2025-08-02 03:38:54,539 - src.historical_data.historical_manager - INFO - Last data for ATOUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:38:54,540 - src.historical_data.historical_manager - INFO - Fetching historical data for ATOUSD_otc (1 days)
2025-08-02 03:38:55,373 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ATOUSD_otc
2025-08-02 03:38:55,373 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:38:55,375 - src.historical_data.historical_manager - INFO - Saved 201 candles for ATOUSD_otc
2025-08-02 03:38:55,375 - src.historical_data.historical_manager - INFO - Updated historical data for ATOUSD_otc
2025-08-02 03:38:57,386 - src.historical_data.historical_manager - INFO - Updating historical data for AUDCAD
2025-08-02 03:38:57,389 - src.historical_data.historical_manager - INFO - Last data for AUDCAD is 0.0 days old, fetching 1 days
2025-08-02 03:38:57,389 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDCAD (1 days)
2025-08-02 03:38:57,939 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDCAD
2025-08-02 03:38:57,940 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:38:57,944 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDCAD
2025-08-02 03:38:57,945 - src.historical_data.historical_manager - INFO - Updated historical data for AUDCAD
2025-08-02 03:38:59,955 - src.historical_data.historical_manager - INFO - Updating historical data for AUDCAD_otc
2025-08-02 03:38:59,955 - src.historical_data.historical_manager - INFO - Last data for AUDCAD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:38:59,955 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDCAD_otc (1 days)
2025-08-02 03:39:00,547 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDCAD_otc
2025-08-02 03:39:00,549 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:39:00,560 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDCAD_otc
2025-08-02 03:39:00,564 - src.historical_data.historical_manager - INFO - Updated historical data for AUDCAD_otc
2025-08-02 03:39:02,579 - src.historical_data.historical_manager - INFO - Updating historical data for AUDCHF
2025-08-02 03:39:02,579 - src.historical_data.historical_manager - INFO - Last data for AUDCHF is 0.0 days old, fetching 1 days
2025-08-02 03:39:02,581 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDCHF (1 days)
2025-08-02 03:39:04,560 - websocket - DEBUG - Sending ping
2025-08-02 03:39:04,780 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDCHF
2025-08-02 03:39:04,783 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:04,787 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDCHF
2025-08-02 03:39:04,787 - src.historical_data.historical_manager - INFO - Updated historical data for AUDCHF
2025-08-02 03:39:06,793 - src.historical_data.historical_manager - INFO - Updating historical data for AUDCHF_otc
2025-08-02 03:39:06,793 - src.historical_data.historical_manager - INFO - Last data for AUDCHF_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:06,793 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDCHF_otc (1 days)
2025-08-02 03:39:08,027 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:39:08,027 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:39:08,027 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:39:08,032 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:39:08,032 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:39:08,789 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDCHF_otc
2025-08-02 03:39:08,793 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:39:08,795 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDCHF_otc
2025-08-02 03:39:08,795 - src.historical_data.historical_manager - INFO - Updated historical data for AUDCHF_otc
2025-08-02 03:39:10,801 - src.historical_data.historical_manager - INFO - Updating historical data for AUDJPY
2025-08-02 03:39:10,801 - src.historical_data.historical_manager - INFO - Last data for AUDJPY is 0.0 days old, fetching 1 days
2025-08-02 03:39:10,801 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDJPY (1 days)
2025-08-02 03:39:11,889 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDJPY
2025-08-02 03:39:11,889 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:11,892 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDJPY
2025-08-02 03:39:11,893 - src.historical_data.historical_manager - INFO - Updated historical data for AUDJPY
2025-08-02 03:39:13,905 - src.historical_data.historical_manager - INFO - Updating historical data for AUDJPY_otc
2025-08-02 03:39:13,907 - src.historical_data.historical_manager - INFO - Last data for AUDJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:13,907 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDJPY_otc (1 days)
2025-08-02 03:39:15,009 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDJPY_otc
2025-08-02 03:39:15,009 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:39:15,009 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDJPY_otc
2025-08-02 03:39:15,009 - src.historical_data.historical_manager - INFO - Updated historical data for AUDJPY_otc
2025-08-02 03:39:17,010 - src.historical_data.historical_manager - INFO - Updating historical data for AUDNZD_otc
2025-08-02 03:39:17,010 - src.historical_data.historical_manager - INFO - Last data for AUDNZD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:17,010 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDNZD_otc (1 days)
2025-08-02 03:39:17,693 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDNZD_otc
2025-08-02 03:39:17,693 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:17,693 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDNZD_otc
2025-08-02 03:39:17,693 - src.historical_data.historical_manager - INFO - Updated historical data for AUDNZD_otc
2025-08-02 03:39:19,697 - src.historical_data.historical_manager - INFO - Updating historical data for AUDUSD
2025-08-02 03:39:19,697 - src.historical_data.historical_manager - INFO - Last data for AUDUSD is 0.0 days old, fetching 1 days
2025-08-02 03:39:19,700 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDUSD (1 days)
2025-08-02 03:39:20,179 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDUSD
2025-08-02 03:39:20,179 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:20,179 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDUSD
2025-08-02 03:39:20,179 - src.historical_data.historical_manager - INFO - Updated historical data for AUDUSD
2025-08-02 03:39:22,181 - src.historical_data.historical_manager - INFO - Updating historical data for AUDUSD_otc
2025-08-02 03:39:22,182 - src.historical_data.historical_manager - INFO - Last data for AUDUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:22,184 - src.historical_data.historical_manager - INFO - Fetching historical data for AUDUSD_otc (1 days)
2025-08-02 03:39:22,811 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AUDUSD_otc
2025-08-02 03:39:22,813 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:39:22,813 - src.historical_data.historical_manager - INFO - Saved 201 candles for AUDUSD_otc
2025-08-02 03:39:22,813 - src.historical_data.historical_manager - INFO - Updated historical data for AUDUSD_otc
2025-08-02 03:39:24,818 - src.historical_data.historical_manager - INFO - Updating historical data for AVAUSD_otc
2025-08-02 03:39:24,819 - src.historical_data.historical_manager - INFO - Last data for AVAUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:24,819 - src.historical_data.historical_manager - INFO - Fetching historical data for AVAUSD_otc (1 days)
2025-08-02 03:39:25,889 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AVAUSD_otc
2025-08-02 03:39:25,889 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:25,896 - src.historical_data.historical_manager - INFO - Saved 201 candles for AVAUSD_otc
2025-08-02 03:39:25,896 - src.historical_data.historical_manager - INFO - Updated historical data for AVAUSD_otc
2025-08-02 03:39:27,897 - src.historical_data.historical_manager - INFO - Updating historical data for AXJAUD
2025-08-02 03:39:27,898 - src.historical_data.historical_manager - INFO - Last data for AXJAUD is 0.0 days old, fetching 1 days
2025-08-02 03:39:27,899 - src.historical_data.historical_manager - INFO - Fetching historical data for AXJAUD (1 days)
2025-08-02 03:39:28,569 - websocket - DEBUG - Sending ping
2025-08-02 03:39:30,057 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AXJAUD
2025-08-02 03:39:30,057 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:30,057 - src.historical_data.historical_manager - INFO - Saved 201 candles for AXJAUD
2025-08-02 03:39:30,057 - src.historical_data.historical_manager - INFO - Updated historical data for AXJAUD
2025-08-02 03:39:32,059 - src.historical_data.historical_manager - INFO - Updating historical data for AXSUSD_otc
2025-08-02 03:39:32,060 - src.historical_data.historical_manager - INFO - Last data for AXSUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:32,060 - src.historical_data.historical_manager - INFO - Fetching historical data for AXSUSD_otc (1 days)
2025-08-02 03:39:34,066 - src.historical_data.historical_manager - INFO - Fetched 199 candles for AXSUSD_otc
2025-08-02 03:39:34,066 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:34,074 - src.historical_data.historical_manager - INFO - Saved 201 candles for AXSUSD_otc
2025-08-02 03:39:34,074 - src.historical_data.historical_manager - INFO - Updated historical data for AXSUSD_otc
2025-08-02 03:39:36,080 - src.historical_data.historical_manager - INFO - Updating historical data for BCHUSD_otc
2025-08-02 03:39:36,080 - src.historical_data.historical_manager - INFO - Last data for BCHUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:36,086 - src.historical_data.historical_manager - INFO - Fetching historical data for BCHUSD_otc (1 days)
2025-08-02 03:39:36,723 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BCHUSD_otc
2025-08-02 03:39:36,724 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:36,727 - src.historical_data.historical_manager - INFO - Saved 201 candles for BCHUSD_otc
2025-08-02 03:39:36,727 - src.historical_data.historical_manager - INFO - Updated historical data for BCHUSD_otc
2025-08-02 03:39:38,732 - src.historical_data.historical_manager - INFO - Updating historical data for BEAUSD_otc
2025-08-02 03:39:38,733 - src.historical_data.historical_manager - INFO - Last data for BEAUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:38,734 - src.historical_data.historical_manager - INFO - Fetching historical data for BEAUSD_otc (1 days)
2025-08-02 03:39:39,394 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BEAUSD_otc
2025-08-02 03:39:39,395 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:39,403 - src.historical_data.historical_manager - INFO - Saved 201 candles for BEAUSD_otc
2025-08-02 03:39:39,405 - src.historical_data.historical_manager - INFO - Updated historical data for BEAUSD_otc
2025-08-02 03:39:41,414 - src.historical_data.historical_manager - INFO - Updating historical data for BNBUSD_otc
2025-08-02 03:39:41,414 - src.historical_data.historical_manager - INFO - Last data for BNBUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:41,414 - src.historical_data.historical_manager - INFO - Fetching historical data for BNBUSD_otc (1 days)
2025-08-02 03:39:41,854 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BNBUSD_otc
2025-08-02 03:39:41,857 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:41,862 - src.historical_data.historical_manager - INFO - Saved 201 candles for BNBUSD_otc
2025-08-02 03:39:41,863 - src.historical_data.historical_manager - INFO - Updated historical data for BNBUSD_otc
2025-08-02 03:39:43,866 - src.historical_data.historical_manager - INFO - Updating historical data for BONUSD_otc
2025-08-02 03:39:43,866 - src.historical_data.historical_manager - INFO - Last data for BONUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:43,868 - src.historical_data.historical_manager - INFO - Fetching historical data for BONUSD_otc (1 days)
2025-08-02 03:39:44,607 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BONUSD_otc
2025-08-02 03:39:44,607 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:44,607 - src.historical_data.historical_manager - INFO - Saved 201 candles for BONUSD_otc
2025-08-02 03:39:44,607 - src.historical_data.historical_manager - INFO - Updated historical data for BONUSD_otc
2025-08-02 03:39:46,608 - src.historical_data.historical_manager - INFO - Updating historical data for BRLUSD_otc
2025-08-02 03:39:46,609 - src.historical_data.historical_manager - INFO - Last data for BRLUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:46,609 - src.historical_data.historical_manager - INFO - Fetching historical data for BRLUSD_otc (1 days)
2025-08-02 03:39:47,046 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BRLUSD_otc
2025-08-02 03:39:47,046 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:47,046 - src.historical_data.historical_manager - INFO - Saved 201 candles for BRLUSD_otc
2025-08-02 03:39:47,046 - src.historical_data.historical_manager - INFO - Updated historical data for BRLUSD_otc
2025-08-02 03:39:49,057 - src.historical_data.historical_manager - INFO - Updating historical data for BTCUSD_otc
2025-08-02 03:39:49,058 - src.historical_data.historical_manager - INFO - Last data for BTCUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:49,059 - src.historical_data.historical_manager - INFO - Fetching historical data for BTCUSD_otc (1 days)
2025-08-02 03:39:49,545 - src.historical_data.historical_manager - INFO - Fetched 199 candles for BTCUSD_otc
2025-08-02 03:39:49,547 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:39:49,552 - src.historical_data.historical_manager - INFO - Saved 201 candles for BTCUSD_otc
2025-08-02 03:39:49,553 - src.historical_data.historical_manager - INFO - Updated historical data for BTCUSD_otc
2025-08-02 03:39:51,569 - src.historical_data.historical_manager - INFO - Updating historical data for CADCHF_otc
2025-08-02 03:39:51,570 - src.historical_data.historical_manager - INFO - Last data for CADCHF_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:51,570 - src.historical_data.historical_manager - INFO - Fetching historical data for CADCHF_otc (1 days)
2025-08-02 03:39:51,999 - src.historical_data.historical_manager - INFO - Fetched 199 candles for CADCHF_otc
2025-08-02 03:39:52,000 - src.historical_data.historical_manager - INFO - Merged 1 new candles with 200 existing candles
2025-08-02 03:39:52,000 - src.historical_data.historical_manager - INFO - Saved 201 candles for CADCHF_otc
2025-08-02 03:39:52,000 - src.historical_data.historical_manager - INFO - Updated historical data for CADCHF_otc
2025-08-02 03:39:52,573 - websocket - DEBUG - Sending ping
2025-08-02 03:39:54,000 - src.historical_data.historical_manager - INFO - Updating historical data for CADJPY
2025-08-02 03:39:54,000 - src.historical_data.historical_manager - INFO - Last data for CADJPY is 0.0 days old, fetching 1 days
2025-08-02 03:39:54,000 - src.historical_data.historical_manager - INFO - Fetching historical data for CADJPY (1 days)
2025-08-02 03:39:54,424 - src.historical_data.historical_manager - INFO - Fetched 199 candles for CADJPY
2025-08-02 03:39:54,424 - src.historical_data.historical_manager - INFO - Merged 1 new candles with 199 existing candles
2025-08-02 03:39:54,424 - src.historical_data.historical_manager - INFO - Saved 200 candles for CADJPY
2025-08-02 03:39:54,424 - src.historical_data.historical_manager - INFO - Updated historical data for CADJPY
2025-08-02 03:39:56,435 - src.historical_data.historical_manager - INFO - Updating historical data for CADJPY_otc
2025-08-02 03:39:56,435 - src.historical_data.historical_manager - INFO - Last data for CADJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:39:56,435 - src.historical_data.historical_manager - INFO - Fetching historical data for CADJPY_otc (1 days)
2025-08-02 03:39:56,764 - src.historical_data.historical_manager - INFO - Fetched 199 candles for CADJPY_otc
2025-08-02 03:39:56,804 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 200 existing candles
2025-08-02 03:39:56,806 - src.historical_data.historical_manager - INFO - Saved 200 candles for CADJPY_otc
2025-08-02 03:39:56,806 - src.historical_data.historical_manager - INFO - Updated historical data for CADJPY_otc
2025-08-02 03:39:58,806 - src.historical_data.historical_manager - INFO - Updating historical data for CHFJPY
2025-08-02 03:39:58,807 - src.historical_data.historical_manager - INFO - Last data for CHFJPY is 0.0 days old, fetching 1 days
2025-08-02 03:39:58,807 - src.historical_data.historical_manager - INFO - Fetching historical data for CHFJPY (1 days)
2025-08-02 03:40:00,116 - src.historical_data.historical_manager - INFO - Fetched 199 candles for CHFJPY
2025-08-02 03:40:00,116 - src.historical_data.historical_manager - INFO - Merged 1 new candles with 199 existing candles
2025-08-02 03:40:00,117 - src.historical_data.historical_manager - INFO - Saved 200 candles for CHFJPY
2025-08-02 03:40:00,117 - src.historical_data.historical_manager - INFO - Updated historical data for CHFJPY
2025-08-02 03:40:02,119 - src.historical_data.historical_manager - INFO - Updating historical data for CHFJPY_otc
2025-08-02 03:40:02,120 - src.historical_data.historical_manager - INFO - Last data for CHFJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:02,120 - src.historical_data.historical_manager - INFO - Fetching historical data for CHFJPY_otc (1 days)
2025-08-02 03:40:04,323 - src.historical_data.historical_manager - INFO - Fetched 200 candles for CHFJPY_otc
2025-08-02 03:40:04,325 - src.historical_data.historical_manager - INFO - Merged 1 new candles with 200 existing candles
2025-08-02 03:40:04,331 - src.historical_data.historical_manager - INFO - Saved 201 candles for CHFJPY_otc
2025-08-02 03:40:04,331 - src.historical_data.historical_manager - INFO - Updated historical data for CHFJPY_otc
2025-08-02 03:40:06,346 - src.historical_data.historical_manager - INFO - Updating historical data for DASUSD_otc
2025-08-02 03:40:06,348 - src.historical_data.historical_manager - INFO - Last data for DASUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:06,348 - src.historical_data.historical_manager - INFO - Fetching historical data for DASUSD_otc (1 days)
2025-08-02 03:40:06,681 - src.historical_data.historical_manager - INFO - Fetched 199 candles for DASUSD_otc
2025-08-02 03:40:06,681 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:06,681 - src.historical_data.historical_manager - INFO - Saved 201 candles for DASUSD_otc
2025-08-02 03:40:06,681 - src.historical_data.historical_manager - INFO - Updated historical data for DASUSD_otc
2025-08-02 03:40:08,032 - src.forex_pairs.pairs_manager - INFO - Updating forex pairs data...
2025-08-02 03:40:08,033 - src.forex_pairs.pairs_manager - INFO - Fetching forex pairs data...
2025-08-02 03:40:08,033 - src.forex_pairs.pairs_manager - INFO - Found 110 forex pairs
2025-08-02 03:40:08,033 - src.forex_pairs.pairs_manager - INFO - Saved 110 forex pairs to data\currency_pairs.json
2025-08-02 03:40:08,033 - src.forex_pairs.pairs_manager - INFO - Forex pairs data updated successfully
2025-08-02 03:40:08,699 - src.historical_data.historical_manager - INFO - Updating historical data for DJIUSD
2025-08-02 03:40:08,700 - src.historical_data.historical_manager - INFO - Last data for DJIUSD is 0.0 days old, fetching 1 days
2025-08-02 03:40:08,700 - src.historical_data.historical_manager - INFO - Fetching historical data for DJIUSD (1 days)
2025-08-02 03:40:09,475 - src.historical_data.historical_manager - INFO - Fetched 199 candles for DJIUSD
2025-08-02 03:40:09,477 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:09,481 - src.historical_data.historical_manager - INFO - Saved 201 candles for DJIUSD
2025-08-02 03:40:09,483 - src.historical_data.historical_manager - INFO - Updated historical data for DJIUSD
2025-08-02 03:40:09,805 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:11,484 - src.historical_data.historical_manager - INFO - Updating historical data for DOGUSD_otc
2025-08-02 03:40:11,484 - src.historical_data.historical_manager - INFO - Last data for DOGUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:11,484 - src.historical_data.historical_manager - INFO - Fetching historical data for DOGUSD_otc (1 days)
2025-08-02 03:40:11,803 - src.historical_data.historical_manager - INFO - Fetched 199 candles for DOGUSD_otc
2025-08-02 03:40:11,803 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:11,803 - src.historical_data.historical_manager - INFO - Saved 201 candles for DOGUSD_otc
2025-08-02 03:40:11,803 - src.historical_data.historical_manager - INFO - Updated historical data for DOGUSD_otc
2025-08-02 03:40:13,678 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:13,805 - src.historical_data.historical_manager - INFO - Updating historical data for DOTUSD_otc
2025-08-02 03:40:13,806 - src.historical_data.historical_manager - INFO - Last data for DOTUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:13,806 - src.historical_data.historical_manager - INFO - Fetching historical data for DOTUSD_otc (1 days)
2025-08-02 03:40:14,151 - src.historical_data.historical_manager - INFO - Fetched 199 candles for DOTUSD_otc
2025-08-02 03:40:14,154 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:14,159 - src.historical_data.historical_manager - INFO - Saved 201 candles for DOTUSD_otc
2025-08-02 03:40:14,160 - src.historical_data.historical_manager - INFO - Updated historical data for DOTUSD_otc
2025-08-02 03:40:16,174 - src.historical_data.historical_manager - INFO - Updating historical data for ETCUSD_otc
2025-08-02 03:40:16,182 - src.historical_data.historical_manager - INFO - Last data for ETCUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:16,183 - src.historical_data.historical_manager - INFO - Fetching historical data for ETCUSD_otc (1 days)
2025-08-02 03:40:16,575 - websocket - DEBUG - Sending ping
2025-08-02 03:40:17,181 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ETCUSD_otc
2025-08-02 03:40:17,183 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:17,189 - src.historical_data.historical_manager - INFO - Saved 201 candles for ETCUSD_otc
2025-08-02 03:40:17,192 - src.historical_data.historical_manager - INFO - Updated historical data for ETCUSD_otc
2025-08-02 03:40:17,860 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:19,194 - src.historical_data.historical_manager - INFO - Updating historical data for ETHUSD_otc
2025-08-02 03:40:19,195 - src.historical_data.historical_manager - INFO - Last data for ETHUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:19,195 - src.historical_data.historical_manager - INFO - Fetching historical data for ETHUSD_otc (1 days)
2025-08-02 03:40:19,861 - src.historical_data.historical_manager - INFO - Fetched 199 candles for ETHUSD_otc
2025-08-02 03:40:19,862 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:19,867 - src.historical_data.historical_manager - INFO - Saved 201 candles for ETHUSD_otc
2025-08-02 03:40:19,867 - src.historical_data.historical_manager - INFO - Updated historical data for ETHUSD_otc
2025-08-02 03:40:20,450 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:21,880 - src.historical_data.historical_manager - INFO - Updating historical data for EURAUD
2025-08-02 03:40:21,881 - src.historical_data.historical_manager - INFO - Last data for EURAUD is 0.0 days old, fetching 1 days
2025-08-02 03:40:21,881 - src.historical_data.historical_manager - INFO - Fetching historical data for EURAUD (1 days)
2025-08-02 03:40:22,442 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURAUD
2025-08-02 03:40:22,442 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:22,442 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURAUD
2025-08-02 03:40:22,442 - src.historical_data.historical_manager - INFO - Updated historical data for EURAUD
2025-08-02 03:40:22,717 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:24,450 - src.historical_data.historical_manager - INFO - Updating historical data for EURAUD_otc
2025-08-02 03:40:24,452 - src.historical_data.historical_manager - INFO - Last data for EURAUD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:24,453 - src.historical_data.historical_manager - INFO - Fetching historical data for EURAUD_otc (1 days)
2025-08-02 03:40:24,897 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURAUD_otc
2025-08-02 03:40:24,898 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:40:24,903 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURAUD_otc
2025-08-02 03:40:24,903 - src.historical_data.historical_manager - INFO - Updated historical data for EURAUD_otc
2025-08-02 03:40:25,053 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:26,913 - src.historical_data.historical_manager - INFO - Updating historical data for EURCAD
2025-08-02 03:40:26,916 - src.historical_data.historical_manager - INFO - Last data for EURCAD is 0.0 days old, fetching 1 days
2025-08-02 03:40:26,916 - src.historical_data.historical_manager - INFO - Fetching historical data for EURCAD (1 days)
2025-08-02 03:40:27,387 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURCAD
2025-08-02 03:40:27,388 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:27,393 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURCAD
2025-08-02 03:40:27,440 - src.historical_data.historical_manager - INFO - Updated historical data for EURCAD
2025-08-02 03:40:29,449 - src.historical_data.historical_manager - INFO - Updating historical data for EURCAD_otc
2025-08-02 03:40:29,449 - src.historical_data.historical_manager - INFO - Last data for EURCAD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:29,449 - src.historical_data.historical_manager - INFO - Fetching historical data for EURCAD_otc (1 days)
2025-08-02 03:40:29,871 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURCAD_otc
2025-08-02 03:40:29,872 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:40:29,873 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURCAD_otc
2025-08-02 03:40:29,873 - src.historical_data.historical_manager - INFO - Updated historical data for EURCAD_otc
2025-08-02 03:40:30,001 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:30,482 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:30,974 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,009 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,044 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,073 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,103 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,132 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,358 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,860 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,880 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,880 - src.historical_data.historical_manager - INFO - Updating historical data for EURCHF
2025-08-02 03:40:31,891 - src.historical_data.historical_manager - INFO - Last data for EURCHF is 0.0 days old, fetching 1 days
2025-08-02 03:40:31,891 - src.historical_data.historical_manager - INFO - Fetching historical data for EURCHF (1 days)
2025-08-02 03:40:31,913 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,943 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:31,977 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:32,011 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:32,032 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:32,656 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURCHF
2025-08-02 03:40:32,657 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:32,665 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURCHF
2025-08-02 03:40:32,665 - src.historical_data.historical_manager - INFO - Updated historical data for EURCHF
2025-08-02 03:40:32,951 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,439 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,462 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,499 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,531 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,563 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,593 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,621 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,651 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,681 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,712 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,739 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:33,762 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:34,294 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:34,695 - src.historical_data.historical_manager - INFO - Updating historical data for EURCHF_otc
2025-08-02 03:40:34,701 - src.historical_data.historical_manager - INFO - Last data for EURCHF_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:34,703 - src.historical_data.historical_manager - INFO - Fetching historical data for EURCHF_otc (1 days)
2025-08-02 03:40:34,800 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:34,833 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:34,864 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:34,895 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:34,920 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:34,948 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:34,982 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:35,017 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:35,039 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:35,078 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:35,083 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURCHF_otc
2025-08-02 03:40:35,083 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:40:35,083 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURCHF_otc
2025-08-02 03:40:35,083 - src.historical_data.historical_manager - INFO - Updated historical data for EURCHF_otc
2025-08-02 03:40:35,101 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:35,130 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:36,104 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:37,079 - src.historical_data.historical_manager - INFO - Updating historical data for EURGBP
2025-08-02 03:40:37,083 - src.historical_data.historical_manager - INFO - Last data for EURGBP is 0.0 days old, fetching 1 days
2025-08-02 03:40:37,085 - src.historical_data.historical_manager - INFO - Fetching historical data for EURGBP (1 days)
2025-08-02 03:40:37,155 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:37,425 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURGBP
2025-08-02 03:40:37,425 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:37,433 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURGBP
2025-08-02 03:40:37,433 - src.historical_data.historical_manager - INFO - Updated historical data for EURGBP
2025-08-02 03:40:37,865 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:38,655 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:39,449 - src.historical_data.historical_manager - INFO - Updating historical data for EURGBP_otc
2025-08-02 03:40:39,450 - src.historical_data.historical_manager - INFO - Last data for EURGBP_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:39,450 - src.historical_data.historical_manager - INFO - Fetching historical data for EURGBP_otc (1 days)
2025-08-02 03:40:39,898 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURGBP_otc
2025-08-02 03:40:39,898 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:40:39,898 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURGBP_otc
2025-08-02 03:40:39,898 - src.historical_data.historical_manager - INFO - Updated historical data for EURGBP_otc
2025-08-02 03:40:40,591 - websocket - DEBUG - Sending ping
2025-08-02 03:40:41,900 - src.historical_data.historical_manager - INFO - Updating historical data for EURJPY
2025-08-02 03:40:41,900 - src.historical_data.historical_manager - INFO - Last data for EURJPY is 0.0 days old, fetching 1 days
2025-08-02 03:40:41,900 - src.historical_data.historical_manager - INFO - Fetching historical data for EURJPY (1 days)
2025-08-02 03:40:42,222 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURJPY
2025-08-02 03:40:42,236 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:42,237 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURJPY
2025-08-02 03:40:42,237 - src.historical_data.historical_manager - INFO - Updated historical data for EURJPY
2025-08-02 03:40:44,212 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:44,212 - src.historical_data.historical_manager - INFO - Updating historical data for EURJPY_otc
2025-08-02 03:40:44,213 - src.historical_data.historical_manager - INFO - Last data for EURJPY_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:44,213 - src.historical_data.historical_manager - INFO - Fetching historical data for EURJPY_otc (1 days)
2025-08-02 03:40:44,547 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURJPY_otc
2025-08-02 03:40:44,547 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:40:44,547 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURJPY_otc
2025-08-02 03:40:44,547 - src.historical_data.historical_manager - INFO - Updated historical data for EURJPY_otc
2025-08-02 03:40:45,290 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:46,357 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:46,553 - src.historical_data.historical_manager - INFO - Updating historical data for EURNZD_otc
2025-08-02 03:40:46,553 - src.historical_data.historical_manager - INFO - Last data for EURNZD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:46,553 - src.historical_data.historical_manager - INFO - Fetching historical data for EURNZD_otc (1 days)
2025-08-02 03:40:46,861 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURNZD_otc
2025-08-02 03:40:46,861 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:46,861 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURNZD_otc
2025-08-02 03:40:46,861 - src.historical_data.historical_manager - INFO - Updated historical data for EURNZD_otc
2025-08-02 03:40:47,290 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:48,454 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:48,876 - src.historical_data.historical_manager - INFO - Updating historical data for EURSGD_otc
2025-08-02 03:40:48,876 - src.historical_data.historical_manager - INFO - Last data for EURSGD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:48,876 - src.historical_data.historical_manager - INFO - Fetching historical data for EURSGD_otc (1 days)
2025-08-02 03:40:49,197 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURSGD_otc
2025-08-02 03:40:49,197 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:49,203 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURSGD_otc
2025-08-02 03:40:49,204 - src.historical_data.historical_manager - INFO - Updated historical data for EURSGD_otc
2025-08-02 03:40:49,378 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:49,728 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,253 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,269 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,295 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,324 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,351 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,380 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,417 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,446 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,469 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,498 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,532 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,564 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,598 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,629 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,656 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,684 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,713 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,746 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,772 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,799 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,832 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,868 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,897 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,920 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,953 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:50,982 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,015 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,047 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,069 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,105 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,136 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,164 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,190 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,190 - src.historical_data.historical_manager - INFO - Updating historical data for EURUSD
2025-08-02 03:40:51,196 - src.historical_data.historical_manager - INFO - Last data for EURUSD is 0.0 days old, fetching 1 days
2025-08-02 03:40:51,198 - src.historical_data.historical_manager - INFO - Fetching historical data for EURUSD (1 days)
2025-08-02 03:40:51,219 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,255 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,283 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,316 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,348 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,370 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,405 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,437 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,467 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,499 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,521 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,554 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,583 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,616 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,668 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,694 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,710 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,736 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,767 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,798 - __main__ - INFO - Received signal 2, shutting down gracefully...
2025-08-02 03:40:51,825 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURUSD
2025-08-02 03:40:51,826 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:51,830 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURUSD
2025-08-02 03:40:51,831 - src.historical_data.historical_manager - INFO - Updated historical data for EURUSD
2025-08-02 03:40:53,832 - src.historical_data.historical_manager - INFO - Updating historical data for EURUSD_otc
2025-08-02 03:40:53,833 - src.historical_data.historical_manager - INFO - Last data for EURUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:53,834 - src.historical_data.historical_manager - INFO - Fetching historical data for EURUSD_otc (1 days)
2025-08-02 03:40:54,155 - src.historical_data.historical_manager - INFO - Fetched 199 candles for EURUSD_otc
2025-08-02 03:40:54,156 - src.historical_data.historical_manager - INFO - Merged 0 new candles with 201 existing candles
2025-08-02 03:40:54,168 - src.historical_data.historical_manager - INFO - Saved 201 candles for EURUSD_otc
2025-08-02 03:40:54,171 - src.historical_data.historical_manager - INFO - Updated historical data for EURUSD_otc
2025-08-02 03:40:56,184 - src.historical_data.historical_manager - INFO - Updating historical data for F40EUR
2025-08-02 03:40:56,194 - src.historical_data.historical_manager - INFO - Last data for F40EUR is 0.0 days old, fetching 1 days
2025-08-02 03:40:56,194 - src.historical_data.historical_manager - INFO - Fetching historical data for F40EUR (1 days)
2025-08-02 03:40:56,586 - src.historical_data.historical_manager - INFO - Fetched 199 candles for F40EUR
2025-08-02 03:40:56,587 - src.historical_data.historical_manager - INFO - Merged 2 new candles with 199 existing candles
2025-08-02 03:40:56,587 - src.historical_data.historical_manager - INFO - Saved 201 candles for F40EUR
2025-08-02 03:40:56,587 - src.historical_data.historical_manager - INFO - Updated historical data for F40EUR
2025-08-02 03:40:58,613 - src.historical_data.historical_manager - INFO - Updating historical data for FLOUSD_otc
2025-08-02 03:40:58,620 - src.historical_data.historical_manager - INFO - Last data for FLOUSD_otc is 0.0 days old, fetching 1 days
2025-08-02 03:40:58,627 - src.historical_data.historical_manager - INFO - Fetching historical data for FLOUSD_otc (1 days)
2025-08-02 03:42:18,032 - __main__ - INFO - Connecting to Quotex platform...
2025-08-02 03:42:19,163 - websocket - INFO - Websocket connected
2025-08-02 03:42:19,194 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-02 03:42:21,727 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-02 03:42:22,209 - __main__ - INFO - Account: Nardelit
2025-08-02 03:42:22,225 - __main__ - INFO - Balance: 10034.12 $
2025-08-02 03:42:22,225 - __main__ - INFO - Initializing data managers...
2025-08-02 03:42:22,225 - __main__ - INFO - Data managers initialized successfully
2025-08-02 03:42:35,645 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-02 03:46:31,071 - __main__ - INFO - Connecting to Quotex platform...
2025-08-02 03:46:32,140 - websocket - INFO - Websocket connected
2025-08-02 03:46:32,161 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-02 03:46:34,692 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-02 03:46:35,194 - __main__ - INFO - Account: Nardelit
2025-08-02 03:46:35,194 - __main__ - INFO - Balance: 10034.12 $
2025-08-02 03:46:35,194 - __main__ - INFO - Initializing data managers...
2025-08-02 03:46:35,194 - __main__ - INFO - Data managers initialized successfully
2025-08-02 03:47:20,172 - websocket - DEBUG - Sending ping
2025-08-02 03:47:44,182 - websocket - DEBUG - Sending ping
2025-08-02 03:48:08,193 - websocket - DEBUG - Sending ping
2025-08-02 03:48:32,197 - websocket - DEBUG - Sending ping
2025-08-02 03:48:56,204 - websocket - DEBUG - Sending ping
2025-08-02 03:49:20,207 - websocket - DEBUG - Sending ping
2025-08-02 03:49:44,216 - websocket - DEBUG - Sending ping
2025-08-02 03:50:08,230 - websocket - DEBUG - Sending ping
2025-08-02 03:50:32,232 - websocket - DEBUG - Sending ping
2025-08-02 03:50:40,569 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-02 03:52:04,055 - __main__ - INFO - Connecting to Quotex platform...
2025-08-02 03:52:06,140 - websocket - INFO - Websocket connected
2025-08-02 03:52:06,191 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-02 03:52:09,227 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-02 03:52:09,731 - __main__ - INFO - Account: Nardelit
2025-08-02 03:52:09,731 - __main__ - INFO - Balance: 10034.12 $
2025-08-02 03:52:09,731 - __main__ - INFO - Initializing data managers...
2025-08-02 03:52:09,731 - __main__ - INFO - Data managers initialized successfully
2025-08-02 03:52:54,190 - websocket - DEBUG - Sending ping
2025-08-02 03:53:18,195 - websocket - DEBUG - Sending ping
2025-08-02 03:53:42,198 - websocket - DEBUG - Sending ping
2025-08-02 03:54:06,205 - websocket - DEBUG - Sending ping
2025-08-02 03:54:30,221 - websocket - DEBUG - Sending ping
2025-08-02 03:54:54,223 - websocket - DEBUG - Sending ping
2025-08-02 03:55:18,233 - websocket - DEBUG - Sending ping
2025-08-02 03:55:42,237 - websocket - DEBUG - Sending ping
2025-08-02 03:56:06,253 - websocket - DEBUG - Sending ping
2025-08-02 03:56:30,262 - websocket - DEBUG - Sending ping
2025-08-02 03:56:54,272 - websocket - DEBUG - Sending ping
2025-08-02 03:57:18,284 - websocket - DEBUG - Sending ping
2025-08-02 03:57:42,299 - websocket - DEBUG - Sending ping
2025-08-02 03:58:06,303 - websocket - DEBUG - Sending ping
2025-08-02 03:58:30,315 - websocket - DEBUG - Sending ping
2025-08-02 03:58:54,321 - websocket - DEBUG - Sending ping
2025-08-02 03:59:18,333 - websocket - DEBUG - Sending ping
2025-08-02 03:59:42,336 - websocket - DEBUG - Sending ping
2025-08-02 04:00:06,355 - websocket - DEBUG - Sending ping
2025-08-02 04:00:30,358 - websocket - DEBUG - Sending ping
2025-08-02 04:00:54,361 - websocket - DEBUG - Sending ping
2025-08-02 04:01:18,363 - websocket - DEBUG - Sending ping
2025-08-02 04:01:42,371 - websocket - DEBUG - Sending ping
2025-08-02 04:02:06,375 - websocket - DEBUG - Sending ping
2025-08-02 04:02:30,379 - websocket - DEBUG - Sending ping
2025-08-02 04:02:54,390 - websocket - DEBUG - Sending ping
2025-08-02 04:03:18,395 - websocket - DEBUG - Sending ping
2025-08-02 04:03:33,266 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-02 04:03:54,086 - __main__ - INFO - Connecting to Quotex platform...
2025-08-02 04:03:55,573 - websocket - INFO - Websocket connected
2025-08-02 04:03:55,641 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-02 04:03:58,175 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-02 04:03:58,740 - __main__ - INFO - Account: Nardelit
2025-08-02 04:03:58,760 - __main__ - INFO - Balance: 10034.12 $
2025-08-02 04:04:43,632 - websocket - DEBUG - Sending ping
2025-08-02 04:05:07,639 - websocket - DEBUG - Sending ping
2025-08-02 04:05:31,644 - websocket - DEBUG - Sending ping
2025-08-02 04:05:55,656 - websocket - DEBUG - Sending ping
2025-08-02 04:06:19,665 - websocket - DEBUG - Sending ping
2025-08-02 04:06:43,681 - websocket - DEBUG - Sending ping
2025-08-02 04:07:07,691 - websocket - DEBUG - Sending ping
2025-08-02 04:07:31,691 - websocket - DEBUG - Sending ping
2025-08-02 04:07:55,699 - websocket - DEBUG - Sending ping
2025-08-02 04:08:19,703 - websocket - DEBUG - Sending ping
2025-08-02 04:08:43,709 - websocket - DEBUG - Sending ping
2025-08-02 04:09:07,710 - websocket - DEBUG - Sending ping
2025-08-02 04:09:31,720 - websocket - DEBUG - Sending ping
2025-08-02 04:09:55,728 - websocket - DEBUG - Sending ping
2025-08-02 04:10:19,741 - websocket - DEBUG - Sending ping
2025-08-02 04:10:28,634 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-02 04:10:36,366 - __main__ - INFO - Connecting to Quotex platform...
2025-08-02 04:10:37,798 - websocket - INFO - Websocket connected
2025-08-02 04:10:37,819 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-02 04:10:40,353 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-02 04:10:40,845 - __main__ - INFO - Account: Nardelit
2025-08-02 04:10:40,845 - __main__ - INFO - Balance: 10034.12 $
2025-08-02 04:11:25,830 - websocket - DEBUG - Sending ping
2025-08-02 04:11:49,839 - websocket - DEBUG - Sending ping
2025-08-02 04:12:13,853 - websocket - DEBUG - Sending ping
2025-08-02 04:12:37,861 - websocket - DEBUG - Sending ping
2025-08-02 04:12:37,943 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-02 04:13:06,765 - __main__ - INFO - Connecting to Quotex platform...
2025-08-02 04:13:07,880 - websocket - INFO - Websocket connected
2025-08-02 04:13:07,933 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-02 04:13:10,952 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-02 04:13:11,438 - __main__ - INFO - Account: Nardelit
2025-08-02 04:13:11,438 - __main__ - INFO - Balance: 10034.12 $
2025-08-02 04:13:48,871 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-04 19:56:24,846 - __main__ - INFO - Connecting to Quotex platform...
2025-08-04 19:56:26,869 - websocket - INFO - Websocket connected
2025-08-04 19:56:26,926 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-04 19:56:27,952 - pyquotex.ws.client - INFO - Disconnection event triggered by the platform, causing automatic reconnection.
2025-08-04 19:56:27,952 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-04 19:56:31,491 - websocket - INFO - Websocket connected
2025-08-04 19:56:31,491 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-04 19:57:17,881 - __main__ - INFO - Successfully connected to Quotex: Websocket Token Rejected.
2025-08-04 19:57:19,514 - websocket - DEBUG - Sending ping
2025-08-04 19:57:20,863 - __main__ - INFO - Account: Nardelit
2025-08-04 19:57:20,863 - __main__ - INFO - Balance: 10034.12 $
2025-08-04 19:57:43,524 - websocket - DEBUG - Sending ping
2025-08-04 19:58:07,534 - websocket - DEBUG - Sending ping
2025-08-04 19:58:31,539 - websocket - DEBUG - Sending ping
2025-08-04 19:58:55,554 - websocket - DEBUG - Sending ping
2025-08-04 19:59:16,562 - pyquotex.ws.client - ERROR - ping/pong timed out
2025-08-04 19:59:16,562 - websocket - INFO - ping/pong timed out - reconnect
2025-08-04 19:59:16,630 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 19:59:16,630 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 19:59:26,617 - websocket - INFO - Websocket connected
2025-08-04 19:59:26,617 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-04 20:00:14,635 - websocket - DEBUG - Sending ping
2025-08-04 20:00:38,642 - websocket - DEBUG - Sending ping
2025-08-04 20:01:02,645 - websocket - DEBUG - Sending ping
2025-08-04 20:01:26,652 - websocket - DEBUG - Sending ping
2025-08-04 20:01:50,655 - websocket - DEBUG - Sending ping
2025-08-04 20:02:14,665 - websocket - DEBUG - Sending ping
2025-08-04 20:02:38,667 - websocket - DEBUG - Sending ping
2025-08-04 20:03:02,677 - websocket - DEBUG - Sending ping
2025-08-04 20:03:21,527 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-04 20:08:58,771 - __main__ - INFO - Connecting to Quotex platform...
2025-08-04 20:09:11,165 - pyquotex.ws.client - ERROR - [Errno 11001] getaddrinfo failed
2025-08-04 20:09:11,189 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-04 20:09:11,236 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:09:11,236 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:09:26,745 - websocket - INFO - Websocket connected
2025-08-04 20:09:26,810 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-04 20:09:26,843 - __main__ - ERROR - Error during connection: socket is already closed.
2025-08-04 20:10:16,145 - __main__ - INFO - Connecting to Quotex platform...
2025-08-04 20:10:35,355 - pyquotex.ws.client - ERROR - Connection to remote host was lost.
2025-08-04 20:10:35,370 - websocket - INFO - Connection to remote host was lost. - reconnect
2025-08-04 20:10:35,442 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:10:35,442 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:10:45,682 - websocket - INFO - Websocket connected
2025-08-04 20:10:45,755 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-04 20:10:48,774 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-04 20:10:56,110 - __main__ - INFO - Account: Nardelit
2025-08-04 20:10:56,110 - __main__ - INFO - Balance: 10034.12 $
2025-08-04 20:11:01,520 - websocket - INFO - [WinError 10060] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond - reconnect
2025-08-04 20:11:01,520 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:11:01,520 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:11:27,562 - websocket - INFO - [WinError 10060] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond - reconnect
2025-08-04 20:11:27,562 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:11:27,562 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:11:33,735 - websocket - DEBUG - Sending ping
2025-08-04 20:11:51,567 - websocket - INFO - [WinError 10054] An existing connection was forcibly closed by the remote host - reconnect
2025-08-04 20:11:51,568 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:11:51,568 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:11:57,743 - websocket - DEBUG - Sending ping
2025-08-04 20:12:08,415 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-04 20:12:08,415 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:12:08,415 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:12:17,752 - pyquotex.ws.client - ERROR - ping/pong timed out
2025-08-04 20:12:17,752 - websocket - INFO - ping/pong timed out - reconnect
2025-08-04 20:12:17,756 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:12:17,756 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:12:33,854 - websocket - INFO - Connection to remote host was lost. - reconnect
2025-08-04 20:12:33,854 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:12:33,854 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:12:43,819 - websocket - INFO - [WinError 10060] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond - reconnect
2025-08-04 20:12:43,819 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:12:43,819 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:12:52,932 - websocket - INFO - Websocket connected
2025-08-04 20:12:52,932 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-04 20:12:52,932 - websocket - ERROR - error from callback <bound method WebsocketClient.on_open of <pyquotex.ws.client.WebsocketClient object at 0x000002E052D6FFB0>>: Connection is already closed.
2025-08-04 20:12:52,932 - pyquotex.ws.client - ERROR - Connection is already closed.
2025-08-04 20:12:52,932 - websocket - INFO - 'NoneType' object has no attribute 'sock' - reconnect
2025-08-04 20:12:52,932 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-04 20:12:59,898 - websocket - INFO - [WinError 10060] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond - reconnect
2025-08-04 20:12:59,898 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-04 20:12:59,898 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-04 20:13:11,023 - websocket - INFO - Websocket connected
2025-08-04 20:13:11,033 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-04 20:13:59,051 - websocket - DEBUG - Sending ping
2025-08-04 20:13:59,283 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-05 17:07:08,370 - __main__ - INFO - Connecting to Quotex platform...
2025-08-05 17:07:11,839 - websocket - INFO - Websocket connected
2025-08-05 17:07:11,900 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-05 17:07:17,532 - pyquotex.ws.client - INFO - Disconnection event triggered by the platform, causing automatic reconnection.
2025-08-05 17:07:17,532 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-05 17:07:21,718 - websocket - INFO - Websocket connected
2025-08-05 17:07:21,718 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-05 17:07:54,852 - __main__ - INFO - Successfully connected to Quotex: Websocket Token Rejected.
2025-08-05 17:07:55,379 - __main__ - INFO - Account: Nardelit
2025-08-05 17:07:55,379 - __main__ - INFO - Balance: 10034.12 $
2025-08-05 17:08:09,746 - websocket - DEBUG - Sending ping
2025-08-05 17:08:33,756 - websocket - DEBUG - Sending ping
2025-08-05 17:08:57,771 - websocket - DEBUG - Sending ping
2025-08-05 17:09:21,799 - websocket - DEBUG - Sending ping
2025-08-05 17:09:45,805 - websocket - DEBUG - Sending ping
2025-08-05 17:10:09,812 - websocket - DEBUG - Sending ping
2025-08-05 17:10:15,164 - pyquotex.ws.client - ERROR - Connection to remote host was lost.
2025-08-05 17:10:15,164 - websocket - INFO - Connection to remote host was lost. - reconnect
2025-08-05 17:10:15,252 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-05 17:10:15,252 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-05 17:10:28,459 - websocket - INFO - Websocket connected
2025-08-05 17:10:28,459 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-05 17:11:01,897 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-06 22:13:05,192 - __main__ - INFO - Connecting to Quotex platform...
2025-08-06 22:13:07,728 - websocket - INFO - Websocket connected
2025-08-06 22:13:07,809 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 22:13:08,630 - pyquotex.ws.client - INFO - Disconnection event triggered by the platform, causing automatic reconnection.
2025-08-06 22:13:08,631 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-06 22:13:12,106 - websocket - INFO - Websocket connected
2025-08-06 22:13:12,118 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 22:13:52,416 - __main__ - INFO - Successfully connected to Quotex: Websocket Token Rejected.
2025-08-06 22:13:52,913 - __main__ - INFO - Account: Nardelit
2025-08-06 22:13:52,913 - __main__ - INFO - Balance: 10034.12 $
2025-08-06 22:14:00,133 - websocket - DEBUG - Sending ping
2025-08-06 22:14:24,149 - websocket - DEBUG - Sending ping
2025-08-06 22:14:48,162 - websocket - DEBUG - Sending ping
2025-08-06 22:15:12,170 - websocket - DEBUG - Sending ping
2025-08-06 22:15:36,177 - websocket - DEBUG - Sending ping
2025-08-06 22:16:00,183 - websocket - DEBUG - Sending ping
2025-08-06 22:16:24,192 - websocket - DEBUG - Sending ping
2025-08-06 22:16:48,198 - websocket - DEBUG - Sending ping
2025-08-06 22:17:12,210 - websocket - DEBUG - Sending ping
2025-08-06 22:17:36,213 - websocket - DEBUG - Sending ping
2025-08-06 22:18:00,222 - websocket - DEBUG - Sending ping
2025-08-06 22:18:24,227 - websocket - DEBUG - Sending ping
2025-08-06 22:18:44,327 - pyquotex.ws.client - ERROR - ping/pong timed out
2025-08-06 22:18:44,327 - websocket - INFO - ping/pong timed out - reconnect
2025-08-06 22:18:44,477 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-06 22:18:44,485 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-06 22:19:00,993 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-06 22:19:00,995 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-06 22:19:00,996 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-06 22:19:26,223 - websocket - INFO - Websocket connected
2025-08-06 22:19:26,223 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 22:20:14,239 - websocket - DEBUG - Sending ping
2025-08-06 22:20:38,250 - websocket - DEBUG - Sending ping
2025-08-06 22:21:02,258 - websocket - DEBUG - Sending ping
2025-08-06 22:21:26,270 - websocket - DEBUG - Sending ping
2025-08-06 22:21:50,275 - websocket - DEBUG - Sending ping
2025-08-06 22:22:14,281 - websocket - DEBUG - Sending ping
2025-08-06 22:22:38,283 - websocket - DEBUG - Sending ping
2025-08-06 22:23:02,291 - websocket - DEBUG - Sending ping
2025-08-06 22:23:26,295 - websocket - DEBUG - Sending ping
2025-08-06 22:23:43,020 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-06 22:33:19,139 - __main__ - INFO - Connecting to Quotex platform...
2025-08-06 22:33:25,104 - websocket - INFO - Websocket connected
2025-08-06 22:33:25,178 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 22:33:30,221 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-06 22:33:37,825 - __main__ - INFO - Account: Nardelit
2025-08-06 22:33:37,825 - __main__ - INFO - Balance: 10034.12 $
2025-08-06 22:34:13,152 - websocket - DEBUG - Sending ping
2025-08-06 22:34:37,153 - websocket - DEBUG - Sending ping
2025-08-06 22:35:01,166 - websocket - DEBUG - Sending ping
2025-08-06 22:35:25,167 - websocket - DEBUG - Sending ping
2025-08-06 22:35:49,182 - websocket - DEBUG - Sending ping
2025-08-06 22:36:13,198 - websocket - DEBUG - Sending ping
2025-08-06 22:36:37,201 - websocket - DEBUG - Sending ping
2025-08-06 22:36:58,126 - pyquotex.ws.client - ERROR - ping/pong timed out
2025-08-06 22:36:58,126 - websocket - INFO - ping/pong timed out - reconnect
2025-08-06 22:36:58,198 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-06 22:36:58,198 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-06 22:37:16,926 - websocket - INFO - Websocket connected
2025-08-06 22:37:16,926 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 22:38:04,946 - websocket - DEBUG - Sending ping
2025-08-06 22:38:28,954 - websocket - DEBUG - Sending ping
2025-08-06 22:38:52,969 - websocket - DEBUG - Sending ping
2025-08-06 22:39:16,984 - websocket - DEBUG - Sending ping
2025-08-06 22:39:40,995 - websocket - DEBUG - Sending ping
2025-08-06 22:40:05,000 - websocket - DEBUG - Sending ping
2025-08-06 22:40:19,357 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-06 22:58:53,230 - __main__ - INFO - Connecting to Quotex platform...
2025-08-06 22:58:54,798 - websocket - INFO - Websocket connected
2025-08-06 22:58:54,850 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 22:58:57,897 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-06 22:58:58,852 - __main__ - INFO - Account: Nardelit
2025-08-06 22:58:58,852 - __main__ - INFO - Balance: 10034.12 $
2025-08-06 22:59:34,017 - pyquotex.ws.client - ERROR - Connection to remote host was lost.
2025-08-06 22:59:34,017 - websocket - INFO - Connection to remote host was lost. - reconnect
2025-08-06 22:59:34,127 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-06 22:59:34,127 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-06 22:59:39,555 - websocket - INFO - Websocket connected
2025-08-06 22:59:39,556 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 23:00:27,559 - websocket - DEBUG - Sending ping
2025-08-06 23:00:51,570 - websocket - DEBUG - Sending ping
2025-08-06 23:01:15,582 - websocket - DEBUG - Sending ping
2025-08-06 23:01:39,591 - websocket - DEBUG - Sending ping
2025-08-06 23:02:03,598 - websocket - DEBUG - Sending ping
2025-08-06 23:02:27,603 - websocket - DEBUG - Sending ping
2025-08-06 23:02:31,209 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-06 23:05:03,048 - __main__ - INFO - Connecting to Quotex platform...
2025-08-06 23:05:04,634 - websocket - INFO - Websocket connected
2025-08-06 23:05:04,692 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 23:05:07,218 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-06 23:05:07,917 - __main__ - INFO - Account: Nardelit
2025-08-06 23:05:07,918 - __main__ - INFO - Balance: 10034.12 $
2025-08-06 23:05:52,665 - websocket - DEBUG - Sending ping
2025-08-06 23:06:16,683 - websocket - DEBUG - Sending ping
2025-08-06 23:06:40,686 - websocket - DEBUG - Sending ping
2025-08-06 23:07:04,701 - websocket - DEBUG - Sending ping
2025-08-06 23:07:28,713 - websocket - DEBUG - Sending ping
2025-08-06 23:07:52,729 - websocket - DEBUG - Sending ping
2025-08-06 23:08:16,734 - websocket - DEBUG - Sending ping
2025-08-06 23:08:40,748 - websocket - DEBUG - Sending ping
2025-08-06 23:09:04,761 - websocket - DEBUG - Sending ping
2025-08-06 23:09:28,763 - websocket - DEBUG - Sending ping
2025-08-06 23:09:52,777 - websocket - DEBUG - Sending ping
2025-08-06 23:10:16,790 - websocket - DEBUG - Sending ping
2025-08-06 23:10:40,796 - websocket - DEBUG - Sending ping
2025-08-06 23:11:04,800 - websocket - DEBUG - Sending ping
2025-08-06 23:11:28,815 - websocket - DEBUG - Sending ping
2025-08-06 23:11:52,821 - websocket - DEBUG - Sending ping
2025-08-06 23:12:16,838 - websocket - DEBUG - Sending ping
2025-08-06 23:12:40,839 - websocket - DEBUG - Sending ping
2025-08-06 23:13:04,842 - websocket - DEBUG - Sending ping
2025-08-06 23:13:28,844 - websocket - DEBUG - Sending ping
2025-08-06 23:13:52,858 - websocket - DEBUG - Sending ping
2025-08-06 23:14:05,463 - pyquotex.ws.client - ERROR - Connection to remote host was lost.
2025-08-06 23:14:05,463 - websocket - INFO - Connection to remote host was lost. - reconnect
2025-08-06 23:14:05,541 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-06 23:14:05,541 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-06 23:14:13,339 - websocket - INFO - Websocket connected
2025-08-06 23:14:13,339 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 23:15:01,357 - websocket - DEBUG - Sending ping
2025-08-06 23:15:25,362 - websocket - DEBUG - Sending ping
2025-08-06 23:15:49,371 - websocket - DEBUG - Sending ping
2025-08-06 23:16:13,379 - websocket - DEBUG - Sending ping
2025-08-06 23:16:37,389 - websocket - DEBUG - Sending ping
2025-08-06 23:17:01,391 - websocket - DEBUG - Sending ping
2025-08-06 23:17:25,404 - websocket - DEBUG - Sending ping
2025-08-06 23:17:49,419 - websocket - DEBUG - Sending ping
2025-08-06 23:18:13,421 - websocket - DEBUG - Sending ping
2025-08-06 23:18:37,432 - websocket - DEBUG - Sending ping
2025-08-06 23:19:01,434 - websocket - DEBUG - Sending ping
2025-08-06 23:19:25,452 - websocket - DEBUG - Sending ping
2025-08-06 23:19:49,467 - websocket - DEBUG - Sending ping
2025-08-06 23:20:13,468 - websocket - DEBUG - Sending ping
2025-08-06 23:20:37,480 - websocket - DEBUG - Sending ping
2025-08-06 23:21:01,486 - websocket - DEBUG - Sending ping
2025-08-06 23:21:25,495 - websocket - DEBUG - Sending ping
2025-08-06 23:21:49,501 - websocket - DEBUG - Sending ping
2025-08-06 23:22:13,511 - websocket - DEBUG - Sending ping
2025-08-06 23:22:37,519 - websocket - DEBUG - Sending ping
2025-08-06 23:23:01,531 - websocket - DEBUG - Sending ping
2025-08-06 23:23:25,833 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-06 23:25:29,260 - __main__ - INFO - Connecting to Quotex platform...
2025-08-06 23:25:30,644 - websocket - INFO - Websocket connected
2025-08-06 23:25:30,676 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-06 23:25:33,206 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-06 23:25:33,834 - __main__ - INFO - Account: Nardelit
2025-08-06 23:25:33,834 - __main__ - INFO - Balance: 10034.12 $
2025-08-06 23:26:18,684 - websocket - DEBUG - Sending ping
2025-08-06 23:26:42,698 - websocket - DEBUG - Sending ping
2025-08-06 23:27:06,712 - websocket - DEBUG - Sending ping
2025-08-06 23:27:30,717 - websocket - DEBUG - Sending ping
2025-08-06 23:27:54,727 - websocket - DEBUG - Sending ping
2025-08-06 23:28:18,736 - websocket - DEBUG - Sending ping
2025-08-06 23:28:42,739 - websocket - DEBUG - Sending ping
2025-08-06 23:29:06,753 - websocket - DEBUG - Sending ping
2025-08-06 23:29:30,761 - websocket - DEBUG - Sending ping
2025-08-06 23:29:54,771 - websocket - DEBUG - Sending ping
2025-08-06 23:30:18,771 - websocket - DEBUG - Sending ping
2025-08-06 23:30:42,787 - websocket - DEBUG - Sending ping
2025-08-06 23:31:06,801 - websocket - DEBUG - Sending ping
2025-08-06 23:31:30,813 - websocket - DEBUG - Sending ping
2025-08-06 23:31:54,816 - websocket - DEBUG - Sending ping
2025-08-06 23:32:18,834 - websocket - DEBUG - Sending ping
2025-08-06 23:32:42,839 - websocket - DEBUG - Sending ping
2025-08-06 23:33:06,847 - websocket - DEBUG - Sending ping
2025-08-06 23:33:30,859 - websocket - DEBUG - Sending ping
2025-08-06 23:33:54,863 - websocket - DEBUG - Sending ping
2025-08-06 23:34:18,872 - websocket - DEBUG - Sending ping
2025-08-06 23:34:42,882 - websocket - DEBUG - Sending ping
2025-08-06 23:35:06,883 - websocket - DEBUG - Sending ping
2025-08-06 23:35:30,886 - websocket - DEBUG - Sending ping
2025-08-06 23:35:54,901 - websocket - DEBUG - Sending ping
2025-08-06 23:36:18,904 - websocket - DEBUG - Sending ping
2025-08-06 23:36:42,918 - websocket - DEBUG - Sending ping
2025-08-06 23:37:06,919 - websocket - DEBUG - Sending ping
2025-08-06 23:37:30,932 - websocket - DEBUG - Sending ping
2025-08-06 23:37:54,940 - websocket - DEBUG - Sending ping
2025-08-06 23:38:18,945 - websocket - DEBUG - Sending ping
2025-08-06 23:38:42,949 - websocket - DEBUG - Sending ping
2025-08-06 23:39:06,956 - websocket - DEBUG - Sending ping
2025-08-06 23:39:30,972 - websocket - DEBUG - Sending ping
2025-08-06 23:39:54,988 - websocket - DEBUG - Sending ping
2025-08-06 23:40:19,003 - websocket - DEBUG - Sending ping
2025-08-06 23:40:43,018 - websocket - DEBUG - Sending ping
2025-08-06 23:41:07,021 - websocket - DEBUG - Sending ping
2025-08-06 23:41:31,030 - websocket - DEBUG - Sending ping
2025-08-06 23:41:55,034 - websocket - DEBUG - Sending ping
2025-08-06 23:41:55,327 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-07 01:40:27,857 - __main__ - INFO - Connecting to Quotex platform...
2025-08-07 01:40:31,392 - websocket - INFO - Websocket connected
2025-08-07 01:40:31,470 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 01:40:32,380 - pyquotex.ws.client - INFO - Disconnection event triggered by the platform, causing automatic reconnection.
2025-08-07 01:40:32,380 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-07 01:40:36,396 - websocket - INFO - Websocket connected
2025-08-07 01:40:36,400 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 01:41:04,869 - __main__ - INFO - Successfully connected to Quotex: Websocket Token Rejected.
2025-08-07 01:41:05,347 - __main__ - INFO - Account: Nardelit
2025-08-07 01:41:05,347 - __main__ - INFO - Balance: 10034.12 $
2025-08-07 01:41:24,412 - websocket - DEBUG - Sending ping
2025-08-07 01:41:48,420 - websocket - DEBUG - Sending ping
2025-08-07 01:42:12,425 - websocket - DEBUG - Sending ping
2025-08-07 01:42:36,441 - websocket - DEBUG - Sending ping
2025-08-07 01:43:00,450 - websocket - DEBUG - Sending ping
2025-08-07 01:43:24,461 - websocket - DEBUG - Sending ping
2025-08-07 01:43:48,473 - websocket - DEBUG - Sending ping
2025-08-07 01:44:12,488 - websocket - DEBUG - Sending ping
2025-08-07 01:44:36,498 - websocket - DEBUG - Sending ping
2025-08-07 01:45:00,512 - websocket - DEBUG - Sending ping
2025-08-07 01:45:24,539 - websocket - DEBUG - Sending ping
2025-08-07 01:45:48,552 - websocket - DEBUG - Sending ping
2025-08-07 01:46:12,563 - websocket - DEBUG - Sending ping
2025-08-07 01:46:36,565 - websocket - DEBUG - Sending ping
2025-08-07 01:47:00,566 - websocket - DEBUG - Sending ping
2025-08-07 01:47:17,291 - pyquotex.ws.client - ERROR - Connection to remote host was lost.
2025-08-07 01:47:17,292 - websocket - INFO - Connection to remote host was lost. - reconnect
2025-08-07 01:47:17,640 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 01:47:17,640 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 01:47:26,298 - websocket - INFO - Websocket connected
2025-08-07 01:47:26,300 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 01:48:14,317 - websocket - DEBUG - Sending ping
2025-08-07 01:48:38,322 - websocket - DEBUG - Sending ping
2025-08-07 01:49:02,332 - websocket - DEBUG - Sending ping
2025-08-07 01:49:26,338 - websocket - DEBUG - Sending ping
2025-08-07 01:49:50,349 - websocket - DEBUG - Sending ping
2025-08-07 01:50:14,361 - websocket - DEBUG - Sending ping
2025-08-07 01:50:38,371 - websocket - DEBUG - Sending ping
2025-08-07 01:51:02,373 - websocket - DEBUG - Sending ping
2025-08-07 01:51:26,388 - websocket - DEBUG - Sending ping
2025-08-07 01:51:50,389 - websocket - DEBUG - Sending ping
2025-08-07 01:52:14,400 - websocket - DEBUG - Sending ping
2025-08-07 01:52:38,408 - websocket - DEBUG - Sending ping
2025-08-07 01:53:02,415 - websocket - DEBUG - Sending ping
2025-08-07 01:53:26,432 - websocket - DEBUG - Sending ping
2025-08-07 01:53:50,441 - websocket - DEBUG - Sending ping
2025-08-07 01:54:14,449 - websocket - DEBUG - Sending ping
2025-08-07 01:54:38,453 - websocket - DEBUG - Sending ping
2025-08-07 01:55:02,458 - websocket - DEBUG - Sending ping
2025-08-07 01:55:26,471 - websocket - DEBUG - Sending ping
2025-08-07 01:55:50,488 - websocket - DEBUG - Sending ping
2025-08-07 01:56:14,489 - websocket - DEBUG - Sending ping
2025-08-07 01:56:38,495 - websocket - DEBUG - Sending ping
2025-08-07 01:57:02,496 - websocket - DEBUG - Sending ping
2025-08-07 01:57:26,509 - websocket - DEBUG - Sending ping
2025-08-07 01:57:50,524 - websocket - DEBUG - Sending ping
2025-08-07 01:58:14,533 - websocket - DEBUG - Sending ping
2025-08-07 01:58:38,541 - websocket - DEBUG - Sending ping
2025-08-07 01:59:02,558 - websocket - DEBUG - Sending ping
2025-08-07 01:59:26,566 - websocket - DEBUG - Sending ping
2025-08-07 01:59:50,569 - websocket - DEBUG - Sending ping
2025-08-07 02:00:14,579 - websocket - DEBUG - Sending ping
2025-08-07 02:00:38,589 - websocket - DEBUG - Sending ping
2025-08-07 02:01:02,591 - websocket - DEBUG - Sending ping
2025-08-07 02:01:26,607 - websocket - DEBUG - Sending ping
2025-08-07 02:01:50,616 - websocket - DEBUG - Sending ping
2025-08-07 02:02:14,625 - websocket - DEBUG - Sending ping
2025-08-07 02:02:38,635 - websocket - DEBUG - Sending ping
2025-08-07 02:03:02,639 - websocket - DEBUG - Sending ping
2025-08-07 02:03:26,652 - websocket - DEBUG - Sending ping
2025-08-07 02:03:50,656 - websocket - DEBUG - Sending ping
2025-08-07 02:04:14,663 - websocket - DEBUG - Sending ping
2025-08-07 02:04:38,678 - websocket - DEBUG - Sending ping
2025-08-07 02:05:02,694 - websocket - DEBUG - Sending ping
2025-08-07 02:05:26,707 - websocket - DEBUG - Sending ping
2025-08-07 02:05:50,717 - websocket - DEBUG - Sending ping
2025-08-07 02:06:14,728 - websocket - DEBUG - Sending ping
2025-08-07 02:06:38,733 - websocket - DEBUG - Sending ping
2025-08-07 02:07:02,741 - websocket - DEBUG - Sending ping
2025-08-07 02:07:26,752 - websocket - DEBUG - Sending ping
2025-08-07 02:07:43,411 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-07 02:19:27,805 - __main__ - INFO - Connecting to Quotex platform...
2025-08-07 02:19:30,156 - websocket - INFO - Websocket connected
2025-08-07 02:19:30,237 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 02:19:32,794 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-07 02:19:33,742 - __main__ - INFO - Account: Nardelit
2025-08-07 02:19:33,742 - __main__ - INFO - Balance: 10034.12 $
2025-08-07 02:20:18,216 - websocket - DEBUG - Sending ping
2025-08-07 02:20:42,218 - websocket - DEBUG - Sending ping
2025-08-07 02:21:06,221 - websocket - DEBUG - Sending ping
2025-08-07 02:21:30,228 - websocket - DEBUG - Sending ping
2025-08-07 02:21:54,241 - websocket - DEBUG - Sending ping
2025-08-07 02:22:18,254 - websocket - DEBUG - Sending ping
2025-08-07 02:22:42,261 - websocket - DEBUG - Sending ping
2025-08-07 02:23:06,265 - websocket - DEBUG - Sending ping
2025-08-07 02:23:30,267 - websocket - DEBUG - Sending ping
2025-08-07 02:23:54,280 - websocket - DEBUG - Sending ping
2025-08-07 02:24:18,287 - websocket - DEBUG - Sending ping
2025-08-07 02:24:42,294 - websocket - DEBUG - Sending ping
2025-08-07 02:25:06,308 - websocket - DEBUG - Sending ping
2025-08-07 02:25:30,326 - websocket - DEBUG - Sending ping
2025-08-07 02:25:54,358 - websocket - DEBUG - Sending ping
2025-08-07 02:26:18,360 - websocket - DEBUG - Sending ping
2025-08-07 02:26:42,367 - websocket - DEBUG - Sending ping
2025-08-07 02:27:06,377 - websocket - DEBUG - Sending ping
2025-08-07 02:27:30,394 - websocket - DEBUG - Sending ping
2025-08-07 02:27:54,402 - websocket - DEBUG - Sending ping
2025-08-07 02:28:18,407 - websocket - DEBUG - Sending ping
2025-08-07 02:28:42,413 - websocket - DEBUG - Sending ping
2025-08-07 02:29:06,418 - websocket - DEBUG - Sending ping
2025-08-07 02:29:30,435 - websocket - DEBUG - Sending ping
2025-08-07 02:29:54,445 - websocket - DEBUG - Sending ping
2025-08-07 02:30:18,462 - websocket - DEBUG - Sending ping
2025-08-07 02:30:42,466 - websocket - DEBUG - Sending ping
2025-08-07 02:31:06,478 - websocket - DEBUG - Sending ping
2025-08-07 02:31:30,485 - websocket - DEBUG - Sending ping
2025-08-07 02:31:54,495 - websocket - DEBUG - Sending ping
2025-08-07 02:32:18,504 - websocket - DEBUG - Sending ping
2025-08-07 02:32:42,512 - websocket - DEBUG - Sending ping
2025-08-07 02:33:06,523 - websocket - DEBUG - Sending ping
2025-08-07 02:33:30,529 - websocket - DEBUG - Sending ping
2025-08-07 02:33:54,538 - websocket - DEBUG - Sending ping
2025-08-07 02:34:18,552 - websocket - DEBUG - Sending ping
2025-08-07 02:34:42,569 - websocket - DEBUG - Sending ping
2025-08-07 02:35:06,583 - websocket - DEBUG - Sending ping
2025-08-07 02:35:30,591 - websocket - DEBUG - Sending ping
2025-08-07 02:35:54,596 - websocket - DEBUG - Sending ping
2025-08-07 02:36:18,603 - websocket - DEBUG - Sending ping
2025-08-07 02:36:42,619 - websocket - DEBUG - Sending ping
2025-08-07 02:37:06,630 - websocket - DEBUG - Sending ping
2025-08-07 02:37:30,632 - websocket - DEBUG - Sending ping
2025-08-07 02:37:54,640 - websocket - DEBUG - Sending ping
2025-08-07 02:38:18,651 - websocket - DEBUG - Sending ping
2025-08-07 02:38:42,662 - websocket - DEBUG - Sending ping
2025-08-07 02:39:06,668 - websocket - DEBUG - Sending ping
2025-08-07 02:39:30,673 - websocket - DEBUG - Sending ping
2025-08-07 02:39:54,688 - websocket - DEBUG - Sending ping
2025-08-07 02:40:18,702 - websocket - DEBUG - Sending ping
2025-08-07 02:40:42,703 - websocket - DEBUG - Sending ping
2025-08-07 02:41:06,718 - websocket - DEBUG - Sending ping
2025-08-07 02:41:30,726 - websocket - DEBUG - Sending ping
2025-08-07 02:41:54,728 - websocket - DEBUG - Sending ping
2025-08-07 02:42:18,732 - websocket - DEBUG - Sending ping
2025-08-07 02:42:42,741 - websocket - DEBUG - Sending ping
2025-08-07 02:43:06,743 - websocket - DEBUG - Sending ping
2025-08-07 02:43:30,750 - websocket - DEBUG - Sending ping
2025-08-07 02:43:54,756 - websocket - DEBUG - Sending ping
2025-08-07 02:44:18,761 - websocket - DEBUG - Sending ping
2025-08-07 02:44:42,763 - websocket - DEBUG - Sending ping
2025-08-07 02:45:06,766 - websocket - DEBUG - Sending ping
2025-08-07 02:45:30,779 - websocket - DEBUG - Sending ping
2025-08-07 02:45:54,780 - websocket - DEBUG - Sending ping
2025-08-07 02:46:18,791 - websocket - DEBUG - Sending ping
2025-08-07 02:46:42,807 - websocket - DEBUG - Sending ping
2025-08-07 02:47:06,813 - websocket - DEBUG - Sending ping
2025-08-07 02:47:30,818 - websocket - DEBUG - Sending ping
2025-08-07 02:47:54,833 - websocket - DEBUG - Sending ping
2025-08-07 02:48:18,844 - websocket - DEBUG - Sending ping
2025-08-07 02:48:42,858 - websocket - DEBUG - Sending ping
2025-08-07 02:49:06,868 - websocket - DEBUG - Sending ping
2025-08-07 02:49:30,877 - websocket - DEBUG - Sending ping
2025-08-07 02:49:54,885 - websocket - DEBUG - Sending ping
2025-08-07 02:50:18,897 - websocket - DEBUG - Sending ping
2025-08-07 02:50:42,909 - websocket - DEBUG - Sending ping
2025-08-07 02:51:06,917 - websocket - DEBUG - Sending ping
2025-08-07 02:51:30,928 - websocket - DEBUG - Sending ping
2025-08-07 02:51:54,937 - websocket - DEBUG - Sending ping
2025-08-07 02:52:18,945 - websocket - DEBUG - Sending ping
2025-08-07 02:52:42,949 - websocket - DEBUG - Sending ping
2025-08-07 02:53:06,963 - websocket - DEBUG - Sending ping
2025-08-07 02:53:30,976 - websocket - DEBUG - Sending ping
2025-08-07 02:53:54,989 - websocket - DEBUG - Sending ping
2025-08-07 02:54:18,995 - websocket - DEBUG - Sending ping
2025-08-07 02:54:43,004 - websocket - DEBUG - Sending ping
2025-08-07 02:55:07,014 - websocket - DEBUG - Sending ping
2025-08-07 02:55:31,030 - websocket - DEBUG - Sending ping
2025-08-07 02:55:55,053 - websocket - DEBUG - Sending ping
2025-08-07 02:56:19,057 - websocket - DEBUG - Sending ping
2025-08-07 02:56:43,060 - websocket - DEBUG - Sending ping
2025-08-07 02:57:07,063 - websocket - DEBUG - Sending ping
2025-08-07 02:57:08,095 - pyquotex.ws.client - ERROR - Connection to remote host was lost.
2025-08-07 02:57:08,097 - websocket - INFO - Connection to remote host was lost. - reconnect
2025-08-07 02:57:08,222 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 02:57:08,222 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 02:57:14,312 - websocket - INFO - Websocket connected
2025-08-07 02:57:14,312 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 02:58:02,326 - websocket - DEBUG - Sending ping
2025-08-07 02:58:26,335 - websocket - DEBUG - Sending ping
2025-08-07 02:58:50,336 - websocket - DEBUG - Sending ping
2025-08-07 02:59:14,345 - websocket - DEBUG - Sending ping
2025-08-07 02:59:38,350 - websocket - DEBUG - Sending ping
2025-08-07 03:00:02,360 - websocket - DEBUG - Sending ping
2025-08-07 03:00:26,366 - websocket - DEBUG - Sending ping
2025-08-07 03:00:50,379 - websocket - DEBUG - Sending ping
2025-08-07 03:01:14,387 - websocket - DEBUG - Sending ping
2025-08-07 03:01:38,396 - websocket - DEBUG - Sending ping
2025-08-07 03:02:02,404 - websocket - DEBUG - Sending ping
2025-08-07 03:02:26,408 - websocket - DEBUG - Sending ping
2025-08-07 03:02:50,421 - websocket - DEBUG - Sending ping
2025-08-07 03:03:14,433 - websocket - DEBUG - Sending ping
2025-08-07 03:03:38,435 - websocket - DEBUG - Sending ping
2025-08-07 03:04:02,438 - websocket - DEBUG - Sending ping
2025-08-07 03:04:26,448 - websocket - DEBUG - Sending ping
2025-08-07 03:04:50,451 - websocket - DEBUG - Sending ping
2025-08-07 03:05:14,462 - websocket - DEBUG - Sending ping
2025-08-07 03:05:38,479 - websocket - DEBUG - Sending ping
2025-08-07 03:06:02,492 - websocket - DEBUG - Sending ping
2025-08-07 03:06:26,503 - websocket - DEBUG - Sending ping
2025-08-07 03:06:50,511 - websocket - DEBUG - Sending ping
2025-08-07 03:07:14,515 - websocket - DEBUG - Sending ping
2025-08-07 03:07:38,515 - websocket - DEBUG - Sending ping
2025-08-07 03:08:02,518 - websocket - DEBUG - Sending ping
2025-08-07 03:08:26,528 - websocket - DEBUG - Sending ping
2025-08-07 03:08:50,537 - websocket - DEBUG - Sending ping
2025-08-07 03:09:14,551 - websocket - DEBUG - Sending ping
2025-08-07 03:09:38,560 - websocket - DEBUG - Sending ping
2025-08-07 03:10:02,571 - websocket - DEBUG - Sending ping
2025-08-07 03:10:26,584 - websocket - DEBUG - Sending ping
2025-08-07 03:10:50,601 - websocket - DEBUG - Sending ping
2025-08-07 03:11:14,613 - websocket - DEBUG - Sending ping
2025-08-07 03:11:38,617 - websocket - DEBUG - Sending ping
2025-08-07 03:12:02,632 - websocket - DEBUG - Sending ping
2025-08-07 03:12:26,642 - websocket - DEBUG - Sending ping
2025-08-07 03:12:50,646 - websocket - DEBUG - Sending ping
2025-08-07 03:13:14,652 - websocket - DEBUG - Sending ping
2025-08-07 03:13:38,657 - websocket - DEBUG - Sending ping
2025-08-07 03:14:02,662 - websocket - DEBUG - Sending ping
2025-08-07 03:14:26,666 - websocket - DEBUG - Sending ping
2025-08-07 03:14:50,671 - websocket - DEBUG - Sending ping
2025-08-07 03:15:14,680 - websocket - DEBUG - Sending ping
2025-08-07 03:15:38,684 - websocket - DEBUG - Sending ping
2025-08-07 03:16:02,694 - websocket - DEBUG - Sending ping
2025-08-07 03:16:26,704 - websocket - DEBUG - Sending ping
2025-08-07 03:16:50,719 - websocket - DEBUG - Sending ping
2025-08-07 03:17:14,727 - websocket - DEBUG - Sending ping
2025-08-07 03:17:38,730 - websocket - DEBUG - Sending ping
2025-08-07 03:18:02,734 - websocket - DEBUG - Sending ping
2025-08-07 03:18:26,739 - websocket - DEBUG - Sending ping
2025-08-07 03:18:50,755 - websocket - DEBUG - Sending ping
2025-08-07 03:19:14,764 - websocket - DEBUG - Sending ping
2025-08-07 03:19:38,766 - websocket - DEBUG - Sending ping
2025-08-07 03:20:02,781 - websocket - DEBUG - Sending ping
2025-08-07 03:20:26,797 - websocket - DEBUG - Sending ping
2025-08-07 03:20:50,799 - websocket - DEBUG - Sending ping
2025-08-07 03:21:14,815 - websocket - DEBUG - Sending ping
2025-08-07 03:21:38,821 - websocket - DEBUG - Sending ping
2025-08-07 03:22:02,829 - websocket - DEBUG - Sending ping
2025-08-07 03:22:26,830 - websocket - DEBUG - Sending ping
2025-08-07 03:22:50,830 - websocket - DEBUG - Sending ping
2025-08-07 03:23:14,843 - websocket - DEBUG - Sending ping
2025-08-07 03:23:38,852 - websocket - DEBUG - Sending ping
2025-08-07 03:24:02,867 - websocket - DEBUG - Sending ping
2025-08-07 03:24:26,876 - websocket - DEBUG - Sending ping
2025-08-07 03:24:47,125 - websocket - INFO - ping/pong timed out - reconnect
2025-08-07 03:24:47,125 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:24:47,127 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:25:03,570 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:25:03,571 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:25:03,572 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:25:20,001 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:25:20,002 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:25:20,002 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:25:25,004 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:25:25,004 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:25:25,004 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:25:30,025 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:25:30,030 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:25:30,031 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:25:46,473 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:25:46,473 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:25:46,473 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:25:51,480 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:25:51,480 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:25:51,480 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:26:07,932 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:26:07,932 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:26:07,932 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:26:12,934 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:26:12,934 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:26:12,934 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:26:29,386 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:26:29,386 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:26:29,386 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:26:34,388 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:26:34,388 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:26:34,388 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:26:50,818 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:26:50,818 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:26:50,818 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:27:07,251 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:27:07,251 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:27:07,251 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:27:12,258 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:27:12,258 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:27:12,258 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:27:29,312 - websocket - INFO - [Errno 11001] getaddrinfo failed - reconnect
2025-08-07 03:27:29,312 - websocket - DEBUG - Calling dispatcher reconnect [4 frames in stack]
2025-08-07 03:27:29,312 - websocket - INFO - reconnect() - retrying in 5 seconds [5 frames in stack]
2025-08-07 03:27:38,588 - websocket - INFO - Websocket connected
2025-08-07 03:27:38,588 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 03:28:26,599 - websocket - DEBUG - Sending ping
2025-08-07 03:28:31,274 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-07 03:30:39,455 - __main__ - INFO - Connecting to Quotex platform...
2025-08-07 03:31:06,275 - websocket - INFO - Websocket connected
2025-08-07 03:31:06,349 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 03:31:08,897 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-07 03:31:29,962 - urllib3.connectionpool - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000228392EFAD0>, 'Connection to qxbroker.com timed out. (connect timeout=None)')': /api/v1/cabinets/digest
2025-08-07 03:31:53,001 - urllib3.connectionpool - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000228392EFD10>, 'Connection to qxbroker.com timed out. (connect timeout=None)')': /api/v1/cabinets/digest
2025-08-07 03:31:54,336 - websocket - DEBUG - Sending ping
2025-08-07 03:32:18,042 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000228392EFE60>, 'Connection to qxbroker.com timed out. (connect timeout=None)')': /api/v1/cabinets/digest
2025-08-07 03:32:18,346 - websocket - DEBUG - Sending ping
2025-08-07 03:32:30,419 - __main__ - ERROR - Error during connection: HTTPSConnectionPool(host='qxbroker.com', port=443): Max retries exceeded with url: /api/v1/cabinets/digest (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x0000022837EBE6F0>: Failed to establish a new connection: [WinError 10053] An established connection was aborted by the software in your host machine'))
2025-08-07 03:32:30,421 - pyquotex.ws.client - ERROR - Connection to remote host was lost.
2025-08-07 03:32:30,421 - websocket - INFO - Connection to remote host was lost. - reconnect
2025-08-07 03:32:30,421 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-07 03:35:25,311 - __main__ - INFO - Connecting to Quotex platform...
2025-08-07 03:35:27,203 - websocket - INFO - Websocket connected
2025-08-07 03:35:27,271 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 03:35:30,309 - __main__ - INFO - Successfully connected to Quotex: Websocket connected successfully!!!
2025-08-07 03:35:30,849 - __main__ - INFO - Account: Nardelit
2025-08-07 03:35:30,850 - __main__ - INFO - Balance: 10034.12 $
2025-08-07 03:36:15,261 - websocket - DEBUG - Sending ping
2025-08-07 03:36:39,272 - websocket - DEBUG - Sending ping
2025-08-07 03:37:03,277 - websocket - DEBUG - Sending ping
2025-08-07 03:37:27,281 - websocket - DEBUG - Sending ping
2025-08-07 03:37:51,291 - websocket - DEBUG - Sending ping
2025-08-07 03:38:15,305 - websocket - DEBUG - Sending ping
2025-08-07 03:38:39,310 - websocket - DEBUG - Sending ping
2025-08-07 03:39:03,315 - websocket - DEBUG - Sending ping
2025-08-07 03:39:27,316 - websocket - DEBUG - Sending ping
2025-08-07 03:39:51,326 - websocket - DEBUG - Sending ping
2025-08-07 03:40:15,330 - websocket - DEBUG - Sending ping
2025-08-07 03:40:39,337 - websocket - DEBUG - Sending ping
