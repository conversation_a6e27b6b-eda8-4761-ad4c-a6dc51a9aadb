{"pair": "EURGBP", "last_updated": "2025-08-07T03:20:01.467359", "candles_count": 14, "has_live_data": true, "indicators": {"EMA5": {"indicator": "EMA5", "parameters": {"period": 5}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "values": [0.873056, 0.8730373333333334, 0.873034888888889, 0.8730332592592595, 0.8730521728395064, 0.8730814485596711, 0.8731009657064475, 0.8731139771376317, 0.8731226514250879, 0.873128434283392, 0.8731322895222614], "current": 0.8731322895222614, "live": true}, "EMA10": {"indicator": "EMA10", "parameters": {"period": 10}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "values": [0.873057, 0.8730720909090909, 0.8730844380165288, 0.8730945401953417, 0.8731028056143705, 0.8731095682299395], "current": 0.8731095682299395, "live": true}, "EMA21": {"indicator": "EMA21", "parameters": {"period": 21}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "value": null, "current": null}, "SMA10": {"indicator": "SMA10", "parameters": {"period": 10}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "value": null, "current": null}, "RSI5": {"indicator": "RSI5", "parameters": {"period": 5}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "values": [14.63414634145417, 21.787709497194086, 21.787709497194086, 38.018815716663795, 49.03589101871768, 49.03589101871768, 49.035891018717685, 49.035891018717685, 49.035891018717685, 49.035891018717685], "current": 49.035891018717685, "live": true}, "RSI14": {"indicator": "RSI14", "parameters": {"period": 14}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "value": null, "current": null}, "MACD": {"indicator": "MACD", "parameters": {"fast_period": 12, "slow_period": 26, "signal_period": 9}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00"}, "Momentum10": {"indicator": "Momentum10", "parameters": {"period": 10}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "values": [99.98282357521556, 100.01832802584252, 100.01259979611238, 100.0171823274035, 100.0171823274035], "current": 100.0171823274035, "live": true}, "BollingerBands": {"indicator": "BollingerBands", "parameters": {"period": 20, "std_dev": 2}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00"}, "ATR5": {"indicator": "ATR5", "parameters": {"period": 5}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "values": [0.00013400000000001188, 7.60000000000094e-05, 6.60000000000105e-05, 7.20000000000054e-05, 7.00000000000145e-05, 6.0000000000015594e-05, 4.80000000000258e-05, 3.60000000000138e-05, 1.6000000000016e-05, 1.6000000000016e-05], "current": 1.6000000000016e-05, "live": true}, "ATR14": {"indicator": "ATR14", "parameters": {"period": 14}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "value": null, "current": null}, "HeikenAshi": {"indicator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": {}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "ha_open": [0.873265, 0.87329625, 0.873128125, 0.8730690624999999, 0.8730407812500001, 0.8730066406250001, 0.8730045703125, 0.87301103515625, 0.8730230175781251, 0.8730440087890625, 0.8730807543945313, 0.8731103771972657, 0.8731251885986329, 0.8731325942993164, 0.8731362971496582], "ha_high": [0.87359, 0.87329625, 0.873128125, 0.8730690624999999, 0.8730407812500001, 0.87303, 0.87305, 0.87307, 0.87312, 0.87316, 0.87314, 0.87314, 0.87314, 0.87314, 0.87314], "ha_low": [0.87319, 0.87294, 0.87296, 0.87298, 0.87291, 0.87298, 0.87299, 0.87301, 0.87302, 0.8730440087890625, 0.8730807543945313, 0.8731103771972657, 0.8731251885986329, 0.8731325942993164, 0.8731362971496582], "ha_close": [0.8733275, 0.87296, 0.87301, 0.8730125000000001, 0.8729725, 0.8730024999999999, 0.8730175, 0.873035, 0.873065, 0.8731175, 0.87314, 0.87314, 0.87314, 0.87314, 0.87314], "current_open": 0.8731362971496582, "current_high": 0.87314, "current_low": 0.8731362971496582, "current_close": 0.87314, "live": true}, "ZScore": {"indicator": "ZScore", "parameters": {"period": 20}, "timestamp": 1754526000, "datetime": "2025-08-07 03:20:00", "value": null, "current": null}}}